# Deployment Guide for Hallucination Station

This guide provides instructions for deploying the Hallucination Station application in different environments.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Variables](#environment-variables)
3. [Local Development Deployment](#local-development-deployment)
4. [Docker Compose Deployment](#docker-compose-deployment)
5. [Cloud Deployment Options](#cloud-deployment-options)
6. [Monitoring and Scaling](#monitoring-and-scaling)
7. [Troubleshooting](#troubleshooting)

## Prerequisites

Before deploying Hallucination Station, ensure you have the following:

- Git
- Docker and Docker Compose (v2.x+)
- Node.js v20+ (for frontend build)
- Python 3.11+ (for backend)
- MongoDB 6.0+ instance
- An AI model file (LLaMa.cpp compatible, like Phi-4-mini-instruct.gguf)

## Environment Variables

Configure the application using the following environment variables:

### Backend Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `MONGODB_URL` | MongoDB connection string | `mongodb://localhost:27017` | Yes |
| `DATABASE_NAME` | MongoDB database name | `hallucination_station` | No |
| `AI_MODEL_PATH` | Path to the LLaMa model file | `./models/phi-4-mini-instruct.gguf` | Yes |
| `SECRET_KEY` | JWT signing key (32+ random chars) | - | Yes |
| `FRONTEND_URL` | URL for frontend (CORS) | `http://localhost:3000` | Yes |
| `ADMIN_EMAIL` | Admin user email | `<EMAIL>` | No |
| `ADMIN_PASSWORD` | Admin user password | `admin123` (dev only) | No |

### Frontend Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `VITE_API_URL` | Backend API URL | `http://localhost:8000` | Yes |
| `VITE_WS_URL` | WebSocket URL | `ws://localhost:8000/ws` | Yes |

## Local Development Deployment

For local development:

1. Clone the repository:
   ```bash
   git clone https://github.com/your-org/hallucination-station.git
   cd hallucination-station
   ```

2. Set up the backend:
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -e .
   ```

3. Download the AI model:
   ```bash
   mkdir -p models
   # Download your chosen model to models/
   # Example: curl -L https://huggingface.co/phi-4-mini-instruct/gguf/resolve/main/phi-4-mini-instruct.Q4_K_M.gguf -o models/phi-4-mini-instruct.gguf
   ```

4. Set up the frontend:
   ```bash
   cd ../frontend
   npm install
   ```

5. Start MongoDB (if not running):
   ```bash
   docker run -d -p 27017:27017 --name mongodb mongo:6
   ```

6. Start the backend:
   ```bash
   cd ../backend
   uvicorn src.main:app --reload
   ```

7. In another terminal, start the frontend:
   ```bash
   cd frontend
   npm run dev
   ```

8. Access the application at http://localhost:5173

## Docker Compose Deployment

For a containerized deployment using Docker Compose:

1. Create a `.env` file in the root directory with your environment variables.

2. Run the application stack:
   ```bash
   docker compose up -d
   ```

3. Access the application at http://localhost:3000

### Docker Compose Configuration

The repository includes a `docker-compose.yml` file in the `infra/` directory:

```yaml
version: '3.8'

services:
  mongodb:
    image: mongo:6
    restart: always
    volumes:
      - mongo_data:/data/db
    ports:
      - "27017:27017"
    healthcheck:
      test: mongosh --eval "db.adminCommand('ping')"
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: ../backend
      dockerfile: Dockerfile
    restart: always
    depends_on:
      mongodb:
        condition: service_healthy
    environment:
      - MONGODB_URL=mongodb://mongodb:27017
      - DATABASE_NAME=hallucination_station
      - AI_MODEL_PATH=/app/models/phi-4-mini-instruct.gguf
      - SECRET_KEY=${SECRET_KEY}
      - FRONTEND_URL=http://localhost:3000
      - ADMIN_EMAIL=${ADMIN_EMAIL}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD}
    volumes:
      - ../models:/app/models
    ports:
      - "8000:8000"
    healthcheck:
      test: curl -f http://localhost:8000/health || exit 1
      interval: 30s
      timeout: 10s
      retries: 3

  frontend:
    build:
      context: ../frontend
      dockerfile: Dockerfile
      args:
        - VITE_API_URL=http://localhost:8000
        - VITE_WS_URL=ws://localhost:8000/ws
    restart: always
    depends_on:
      - backend
    ports:
      - "3000:80"

  # Optional AI model server for larger models
  ai-model:
    image: ghcr.io/ggerganov/llama.cpp:server
    restart: always
    volumes:
      - ../models:/models
    command: --host 0.0.0.0 --port 8080 --model /models/phi-4-mini-instruct.gguf --ctx-size 2048 --threads 4
    ports:
      - "8080:8080"
    deploy:
      resources:
        limits:
          cpus: '4'
          memory: 8G

volumes:
  mongo_data:
```

## Cloud Deployment Options

### Render

To deploy on Render:

1. Create a new Web Service for the backend:
   - Connect your GitHub repository
   - Build Command: `pip install -e .`
   - Start Command: `uvicorn src.main:app --host 0.0.0.0 --port $PORT`
   - Add environment variables as listed above

2. Create a new Static Site for the frontend:
   - Connect your GitHub repository
   - Build Command: `cd frontend && npm install && npm run build`
   - Publish Directory: `frontend/dist`
   - Environment Variables: Set `VITE_API_URL` to your backend URL

3. Create a MongoDB database using Render's managed database service or MongoDB Atlas.

### Railway

To deploy on Railway:

1. Create a new project and add your GitHub repository.
2. Add a MongoDB service.
3. Configure the backend service:
   - Root Directory: `backend`
   - Start Command: `uvicorn src.main:app --host 0.0.0.0 --port $PORT`
   - Add environment variables as needed
4. Configure the frontend service:
   - Root Directory: `frontend`
   - Build Command: `npm install && npm run build`
   - Start Command: `npx serve -s dist`
   - Add environment variables as needed

## Monitoring and Scaling

### Application Metrics

The backend includes metrics endpoints at `/health` and provides response time headers to track API performance.

For production monitoring:
- Consider adding Prometheus metrics using the FastAPI Prometheus middleware
- Set up Grafana dashboards for visualization
- Implement logging to a central service like ELK Stack or DataDog

### Scaling Strategies

The application can be scaled in multiple ways:

1. **Vertical scaling**: Increase resources (CPU/RAM) for the backend service.
2. **Horizontal scaling**: Run multiple backend instances behind a load balancer.
3. **AI model optimization**: For larger deployments, consider:
   - Using the optional AI model server component
   - Implementing a model server pool with load balancing
   - Optimizing model quantization for your hardware

## Troubleshooting

### Common Issues

1. **MongoDB connection errors**:
   - Check MongoDB is running and accessible
   - Verify connection string format
   - Ensure database user has correct permissions

2. **AI model loading errors**:
   - Confirm model file exists at the configured path
   - Check for sufficient memory (at least 4GB for the default model)
   - Try a smaller quantized model if memory is constrained

3. **WebSocket connection issues**:
   - Verify WebSocket URL is correct
   - Check if proxies or firewalls are blocking WebSocket traffic
   - Ensure CORS is properly configured

For additional help, check application logs or create an issue in the GitHub repository.