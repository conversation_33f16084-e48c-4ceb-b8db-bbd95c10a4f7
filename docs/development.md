# Development Guide for Hallucination Station

This guide provides instructions for setting up and contributing to the Hallucination Station application.

## Table of Contents

1. [Development Environment Setup](#development-environment-setup)
2. [Project Structure](#project-structure)
3. [Development Workflow](#development-workflow)
4. [Testing](#testing)
5. [Code Style & Linting](#code-style--linting)
6. [API Documentation](#api-documentation)
7. [Frontend Development](#frontend-development)
8. [Backend Development](#backend-development)
9. [Database Management](#database-management)
10. [AI Model Integration](#ai-model-integration)
11. [Contribution Guidelines](#contribution-guidelines)

## Development Environment Setup

### Prerequisites

- Python 3.11+
- Node.js 20+ and npm
- Git
- MongoDB 6.0+
- <PERSON><PERSON> and <PERSON><PERSON> Compose (optional)

### Backend Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/your-org/hallucination-station.git
   cd hallucination-station
   ```

2. Set up a virtual environment:
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -e ".[dev]"
   ```

4. Download the AI model:
   ```bash
   mkdir -p models
   # Download a compatible model to the models/ directory
   # For Phi-2 (recommended for development):
   wget -c https://huggingface.co/TheBloke/phi-2-GGUF/resolve/main/phi-2.Q4_K_M.gguf -O models/phi-2.Q4_K_M.gguf
   # Or use curl:
   # curl -L https://huggingface.co/TheBloke/phi-2-GGUF/resolve/main/phi-2.Q4_K_M.gguf -o models/phi-2.Q4_K_M.gguf
   ```

5. Set up environment variables:
   ```bash
   export MONGODB_URL="mongodb://localhost:27017"
   export DATABASE_NAME="hallucination_station"
   export AI_MODEL_PATH="./models/phi-4-mini-instruct.gguf"
   export SECRET_KEY="your-dev-secret-key"
   ```

6. Start the backend:
   ```bash
   uvicorn src.main:app --reload
   ```

### Frontend Setup

1. Navigate to the frontend directory:
   ```bash
   cd ../frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   Create a `.env.development` file:
   ```
   VITE_API_URL=http://localhost:8000
   VITE_WS_URL=ws://localhost:8000/ws
   ```

4. Start the development server:
   ```bash
   npm run dev
   ```

5. Access the application at http://localhost:5173

### Docker Setup (Alternative)

For development with Docker:

1. Navigate to the root directory
2. Download the required model and start the development environment using our helper script:
   ```bash
   chmod +x start-dev.sh
   ./start-dev.sh
   ```

   Alternatively, if you've already downloaded the model manually, you can just run:
   ```bash
   docker compose -f infra/docker-compose.dev.yml up
   ```

## Project Structure

```
hallucination_station/
├── backend/                 # Backend Python code
│   ├── src/
│   │   ├── api/             # API routes
│   │   ├── core/            # Core utilities
│   │   ├── models/          # MongoDB models
│   │   ├── schemas/         # Pydantic schemas
│   │   ├── services/        # Business logic
│   │   └── main.py          # Application entry point
│   ├── tests/
│   │   ├── contract/        # API contract tests
│   │   ├── integration/     # Integration tests
│   │   ├── load/            # Load tests
│   │   ├── performance/     # Performance benchmarks
│   │   └── unit/            # Unit tests
│   ├── pyproject.toml       # Python dependencies
│   └── .ruff.toml           # Ruff linter config
├── frontend/                # Frontend React code
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── pages/           # Page components
│   │   ├── services/        # API clients
│   │   ├── hooks/           # React hooks
│   │   ├── lib/             # Utilities
│   │   └── types/           # TypeScript types
│   ├── tests/
│   │   ├── e2e/             # E2E tests
│   │   └── unit/            # Unit tests
│   ├── package.json         # Node dependencies
│   └── tsconfig.json        # TypeScript config
├── infra/                   # Infrastructure files
│   ├── docker-compose.yml          # Production compose
│   └── docker-compose.dev.yml      # Development compose
├── docs/                    # Documentation
├── models/                  # AI model files
└── specs/                   # Specifications and contracts
    └── 001-build-an-application/
        ├── contracts/       # API contracts
        ├── data-model.md    # Data models
        ├── plan.md          # Implementation plan
        └── research.md      # Research notes
```

## Development Workflow

We follow a test-driven development (TDD) approach:

1. Write contract tests for new API endpoints
2. Implement the necessary models and schemas
3. Add business logic in services
4. Create API routes
5. Implement frontend components
6. Add integration and E2E tests

### Git Workflow

1. Create a feature branch from the main branch:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Make changes and commit regularly:
   ```bash
   git add .
   git commit -m "feat: your feature description"
   ```

3. Keep your branch up to date:
   ```bash
   git pull origin main
   git merge main
   ```

4. Push your branch and create a Pull Request:
   ```bash
   git push -u origin feature/your-feature-name
   ```

## Testing

### Backend Tests

The backend includes several types of tests:

1. **Contract Tests**: Ensure API endpoints match the contract
   ```bash
   pytest backend/tests/contract/
   ```

2. **Unit Tests**: Test individual functions and services
   ```bash
   pytest backend/tests/unit/
   ```

3. **Integration Tests**: Test component interactions
   ```bash
   pytest backend/tests/integration/
   ```

4. **Load Tests**: Verify performance under load
   ```bash
   pytest backend/tests/load/
   ```

### Frontend Tests

1. **Unit Tests**: Test React components
   ```bash
   cd frontend
   npm test
   ```

2. **End-to-End Tests**: Full application testing with Playwright
   ```bash
   cd frontend
   npm run test:e2e
   ```

## Code Style & Linting

### Backend

We use Ruff for linting, formatting, and type checking:

```bash
cd backend
ruff check .
ruff format .
```

### Frontend

We use ESLint and Prettier for JavaScript/TypeScript:

```bash
cd frontend
npm run lint
npm run format
```

## API Documentation

API documentation is auto-generated from FastAPI routes and the OpenAPI schema:

- Interactive documentation: http://localhost:8000/docs
- ReDoc alternative: http://localhost:8000/redoc

The API documentation is enriched with details from the API contract in `specs/001-build-an-application/contracts/api-contract.yaml`.

## Frontend Development

The frontend is built with:

- React 18
- TypeScript 5
- TailwindCSS
- shadcn/ui components
- React Query for data fetching
- Socket.io for WebSocket connections

### Component Development

When creating new components:

1. Add TypeScript interfaces in `src/types/`
2. Create the component in `src/components/`
3. Add unit tests in `tests/unit/`
4. Update the relevant pages to use the component

## Backend Development

The backend is built with:

- FastAPI
- MongoDB with Motor (async driver)
- Pydantic for data validation
- LLaMa.cpp for AI model integration

### Adding New Endpoints

When adding new API endpoints:

1. Add contract tests in `tests/contract/`
2. Create/update Pydantic schemas in `src/schemas/`
3. Implement business logic in `src/services/`
4. Add API routes in `src/api/`
5. Update API documentation in the docstrings

## Database Management

We use MongoDB with Motor for async operations:

1. Models are defined in `src/models/`
2. MongoDB operations are encapsulated in services
3. Indexes are created during application startup

To work with the database directly:

```bash
# Using mongosh
mongosh "mongodb://localhost:27017/hallucination_station"
```

## AI Model Integration

The application uses LLaMa.cpp to run small to medium-sized AI models locally:

1. Models should be placed in the `models/` directory
2. The default model is Phi-4-mini-instruct.gguf
3. Model inference is managed by the `AIService` in `src/services/ai_service.py`

For larger models or production deployments, consider:
- Using the optional AI model server component
- Connecting to a hosted inference API
- Optimizing model quantization for your hardware

## Contribution Guidelines

1. **Issues**: Check existing issues or create new ones before starting work
2. **Pull Requests**: Reference issues in PR descriptions
3. **Commits**: Follow conventional commits style:
   - `feat:` for new features
   - `fix:` for bug fixes
   - `docs:` for documentation
   - `test:` for adding tests
   - `refactor:` for refactoring code
   - `style:` for formatting changes
   - `chore:` for maintenance tasks

4. **Code Review**: PRs require at least one review
5. **Tests**: All PRs must pass existing tests and include new tests
6. **Documentation**: Update docs when adding or changing features