# hallucination_station Development Guidelines

Auto-generated from all feature plans. Last updated: 2025-09-30

## Active Technologies
- Python 3.11+, TypeScript 5.x, Node.js 20+ (frontend tooling) (001-build-an-application)

## Project Structure
```
backend/
frontend/
tests/
```

## Commands
cd src [ONLY COMMANDS FOR ACTIVE TECHNOLOGIES][ONLY COMMANDS FOR ACTIVE TECHNOLOGIES] pytest [ONLY COMMANDS FOR ACTIVE TECHNOLOGIES][ONLY COMMANDS FOR ACTIVE TECHNOLOGIES] ruff check .
ALWAYS USE uv run to run code or python3 to run code.

## Code Style
Python 3.11+, TypeScript 5.x, Node.js 20+ (frontend tooling): Follow standard conventions

## Recent Changes
- 001-build-an-application: Added Python 3.11+, TypeScript 5.x, Node.js 20+ (frontend tooling)

<!-- MANUAL ADDITIONS START -->
<!-- MANUAL ADDITIONS END -->

## Rules to better use of the tokens.
Rule 1 - one chat window per task. This case always recommend /clear command

Rule 2 - Summarize chats before they get long. Once reach ~50% context limit, summarize the conversation with /compact and start a new one. recommend what is better to be summarized.

Rule 3 - Use the powerful models like Sonnet 4.5 to algorithms, debbuging, system design, code optimizations, refactoring. Use the Sonnet 3.7 to Simple implementation, coding based in a plan (specs), documentaton, syntax and formatting

## Code Style
- ALWAYS use docstring in functions to understand, but don't use comments in #.

