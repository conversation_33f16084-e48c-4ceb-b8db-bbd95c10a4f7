[tool.poetry]
name = "hallucination-station-backend"
version = "0.1.0"
description = "Backend for the Hallucination Station project"
authors = ["Your Name <<EMAIL>>"]
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.11"
fastapi = "^0.109.2"
uvicorn = {extras = ["standard"], version = "^0.27.1"}
motor = "^3.3.2"
pydantic = "^2.6.1"
pydantic-ai = "^0.1.0"
llama-cpp-python = "^0.2.44"
python-jose = {extras = ["cryptography"], version = "^3.3.0"}
passlib = {extras = ["bcrypt"], version = "^1.7.4"}
bcrypt = ">=4.0.0,<5.0.0"
email-validator = "^2.1.0"
structlog = "^24.1.0"
python-json-logger = "^2.0.7"

[tool.poetry.group.dev.dependencies]
pytest = "^8.0.0"
ruff = "^0.2.1"
black = "^24.2.0"
mypy = "^1.8.0"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.ruff]
line-length = 88

[tool.black]
line-length = 88

[tool.mypy]
strict = true
warnings_as_errors = true