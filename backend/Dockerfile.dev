FROM python:3.11-slim

WORKDIR /app

# Install system dependencies needed for building packages
RUN apt-get update && apt-get install -y \
    build-essential \
    gcc \
    g++ \
    cmake \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install poetry
RUN pip install poetry

# Install email-validator directly
RUN pip install email-validator

# Copy only pyproject.toml to leverage Docker cache
COPY pyproject.toml ./

# Configure poetry to not use virtualenvs
RUN poetry config virtualenvs.create false

# Install dependencies without the current project
RUN poetry install --no-root --no-interaction --no-ansi

# Create src directory structure
RUN mkdir -p src

# Expose the port the app runs on
EXPOSE 8000

# Command to run the application in development mode
CMD ["uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]