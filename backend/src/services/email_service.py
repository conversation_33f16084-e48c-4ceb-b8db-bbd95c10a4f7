"""Email service for verification and password reset emails."""

import logging
from typing import Optional

logger = logging.getLogger(__name__)


class EmailService:
    """Service for sending emails (FR-002, FR-004)."""

    def __init__(self, smtp_host: Optional[str] = None, smtp_port: int = 587):
        """
        Initialize email service.

        Args:
            smtp_host: SMTP server hostname
            smtp_port: SMTP server port
        """
        self.smtp_host = smtp_host
        self.smtp_port = smtp_port
        self.from_email = "<EMAIL>"

    async def send_verification_email(self, to_email: str, token: str) -> bool:
        """
        Send email verification email (FR-002).

        Args:
            to_email: Recipient email
            token: Verification token

        Returns:
            True if sent successfully
        """
        verify_url = f"https://hallucination-station.example.com/verify-email?token={token}"

        subject = "Verify Your Email - Hallucination Station"
        body = f"""
        Welcome to Hallucination Station!

        Please verify your email address by clicking the link below:
        {verify_url}

        This link will expire in 24 hours.

        If you didn't create an account, please ignore this email.

        Happy hallucinating!
        The Hallucination Station Team
        """

        return await self._send_email(to_email, subject, body)

    async def send_password_reset_email(self, to_email: str, token: str) -> bool:
        """
        Send password reset email (FR-004).

        Args:
            to_email: Recipient email
            token: Reset token

        Returns:
            True if sent successfully
        """
        reset_url = f"https://hallucination-station.example.com/reset-password?token={token}"

        subject = "Reset Your Password - Hallucination Station"
        body = f"""
        You requested to reset your password.

        Click the link below to create a new password:
        {reset_url}

        This link will expire in 1 hour.

        If you didn't request a password reset, please ignore this email.
        Your password will remain unchanged.

        The Hallucination Station Team
        """

        return await self._send_email(to_email, subject, body)

    async def _send_email(self, to_email: str, subject: str, body: str) -> bool:
        """
        Internal method to send email.

        Args:
            to_email: Recipient
            subject: Email subject
            body: Email body

        Returns:
            True if sent successfully

        Note:
            This is a placeholder implementation.
            In production, integrate with SMTP server or service like SendGrid/AWS SES.
        """
        # TODO: Implement actual email sending
        # For MVP, log emails instead of sending
        logger.info(
            f"[EMAIL] To: {to_email}\nSubject: {subject}\nBody:\n{body}\n---"
        )

        # In production, use smtplib or email service API:
        # import smtplib
        # from email.mime.text import MIMEText
        #
        # msg = MIMEText(body)
        # msg['Subject'] = subject
        # msg['From'] = self.from_email
        # msg['To'] = to_email
        #
        # with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
        #     server.starttls()
        #     server.login(username, password)
        #     server.send_message(msg)

        return True

    def verify_token(self, token: str) -> bool:
        """
        Verify if token format is valid.

        Args:
            token: Token string

        Returns:
            True if valid format
        """
        # Basic validation (token should be URL-safe base64)
        return len(token) > 20 and token.replace("-", "").replace("_", "").isalnum()
