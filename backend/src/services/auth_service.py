"""Authentication service for user registration, login, and JWT token management."""

import secrets
from datetime import datetime, timedelta
from typing import Optional

from bson import ObjectId
from jose import JWTError, jwt
from motor.motor_asyncio import AsyncIOMotorDatabase
from passlib.context import Crypt<PERSON>ontext

from ..models.user import User, UserConsent, UserInDB

# Password hashing context with bcrypt (12 rounds per research.md)
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto", bcrypt__rounds=12)

# JWT configuration (15min access, 7day refresh per research.md)
SECRET_KEY = "your-secret-key-here"  # TODO: Load from environment
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 15
REFRESH_TOKEN_EXPIRE_DAYS = 7


class AuthService:
    """Service for authentication operations."""

    def __init__(self, db: AsyncIOMotorDatabase):
        """Initialize auth service with database."""
        self.db = db
        self.users_collection = db.users

    def hash_password(self, password: str) -> str:
        """Hash password using bcrypt with 12 rounds."""
        return pwd_context.hash(password)

    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify password against hash."""
        return pwd_context.verify(plain_password, hashed_password)

    def create_access_token(
        self, user_id: str, expires_delta: Optional[timedelta] = None
    ) -> str:
        """Create JWT access token."""
        if expires_delta is None:
            expires_delta = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)

        expire = datetime.utcnow() + expires_delta
        to_encode = {"sub": user_id, "exp": expire, "type": "access"}

        return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

    def create_refresh_token(self, user_id: str) -> str:
        """Create JWT refresh token."""
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode = {"sub": user_id, "exp": expire, "type": "refresh"}

        return jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)

    def verify_token(self, token: str, token_type: str = "access") -> Optional[str]:
        """
        Verify JWT token and return user_id.

        Args:
            token: JWT token string
            token_type: Expected token type ("access" or "refresh")

        Returns:
            user_id if valid, None otherwise
        """
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            user_id: str = payload.get("sub")
            token_type_claim: str = payload.get("type")

            if user_id is None or token_type_claim != token_type:
                return None

            return user_id
        except JWTError:
            return None

    async def register_user(
        self,
        email: str,
        password: str,
        display_name: str,
        consent_given: bool,
        ip_address: str,
    ) -> UserInDB:
        """
        Register new user with email/password (FR-001, FR-002).

        Args:
            email: User email
            password: Plain password (will be hashed)
            display_name: User's display name
            consent_given: GDPR/LGPD consent (FR-041)
            ip_address: User's IP for consent tracking

        Returns:
            Created user

        Raises:
            ValueError: If email already exists or consent not given
        """
        # Check if user exists
        existing = await self.users_collection.find_one({"email": email})
        if existing:
            raise ValueError("Email already registered")

        if not consent_given:
            raise ValueError("User consent required (GDPR/LGPD)")

        # Create user document
        user = User(
            email=email,
            password_hash=self.hash_password(password),
            display_name=display_name,
            email_verified=False,
            verification_token=secrets.token_urlsafe(32),
            consent=UserConsent(
                given=consent_given, date=datetime.utcnow(), ip_address=ip_address
            ),
        )

        # Insert to database
        result = await self.users_collection.insert_one(
            user.model_dump(by_alias=True, exclude={"id"})
        )
        user.id = result.inserted_id

        return UserInDB(**user.model_dump())

    async def login_user(self, email: str, password: str) -> Optional[UserInDB]:
        """
        Authenticate user with email/password (FR-003).

        Args:
            email: User email
            password: Plain password

        Returns:
            User if authenticated, None otherwise
        """
        user_doc = await self.users_collection.find_one({"email": email})
        if not user_doc:
            return None

        user = UserInDB(**user_doc)

        if not user.password_hash or not self.verify_password(
            password, user.password_hash
        ):
            return None

        # Update last login
        await self.users_collection.update_one(
            {"_id": user.id}, {"$set": {"last_login": datetime.utcnow()}}
        )

        return user

    async def refresh_access_token(self, refresh_token: str) -> Optional[str]:
        """
        Refresh access token using refresh token (FR-006).

        Args:
            refresh_token: Valid refresh token

        Returns:
            New access token if refresh token valid, None otherwise
        """
        user_id = self.verify_token(refresh_token, token_type="refresh")
        if not user_id:
            return None

        # Verify user still exists
        user = await self.users_collection.find_one({"_id": ObjectId(user_id)})
        if not user:
            return None

        return self.create_access_token(user_id)

    async def get_user_by_id(self, user_id: str) -> Optional[UserInDB]:
        """Get user by ID."""
        try:
            user_doc = await self.users_collection.find_one({"_id": ObjectId(user_id)})
            if not user_doc:
                return None
            return UserInDB(**user_doc)
        except Exception:
            return None

    async def verify_email(self, token: str) -> bool:
        """
        Verify user email with verification token (FR-002).

        Args:
            token: Email verification token

        Returns:
            True if verified, False otherwise
        """
        result = await self.users_collection.update_one(
            {"verification_token": token},
            {
                "$set": {
                    "email_verified": True,
                    "verification_token": None,
                    "updated_at": datetime.utcnow(),
                }
            },
        )

        return result.modified_count > 0

    async def create_password_reset_token(self, email: str) -> Optional[str]:
        """
        Create password reset token (FR-004).

        Args:
            email: User email

        Returns:
            Reset token if user exists, None otherwise
        """
        user = await self.users_collection.find_one({"email": email})
        if not user:
            # Return None but don't reveal if email exists (security)
            return None

        reset_token = secrets.token_urlsafe(32)

        # Store token with expiry (1 hour)
        await self.users_collection.update_one(
            {"_id": user["_id"]},
            {
                "$set": {
                    "reset_token": reset_token,
                    "reset_token_expires": datetime.utcnow() + timedelta(hours=1),
                }
            },
        )

        return reset_token

    async def reset_password(self, token: str, new_password: str) -> bool:
        """
        Reset user password with token.

        Args:
            token: Password reset token
            new_password: New password (will be hashed)

        Returns:
            True if reset successful, False otherwise
        """
        user = await self.users_collection.find_one(
            {
                "reset_token": token,
                "reset_token_expires": {"$gt": datetime.utcnow()},
            }
        )

        if not user:
            return False

        # Update password and clear reset token
        await self.users_collection.update_one(
            {"_id": user["_id"]},
            {
                "$set": {
                    "password_hash": self.hash_password(new_password),
                    "reset_token": None,
                    "reset_token_expires": None,
                    "updated_at": datetime.utcnow(),
                }
            },
        )

        return True
