"""Chat service for managing solo chat sessions."""

from datetime import datetime
from typing import Optional
from uuid import uuid4

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from ..core.logging import get_logger
from ..models.chat_session import ChatMessage, ChatSession, ChatSessionInDB, PromptMessage, ResponseMessage

logger = get_logger(__name__)


class ChatService:
    """Service for chat session management (FR-008 to FR-015, FR-020)."""

    def __init__(self, db: AsyncIOMotorDatabase):
        """Initialize chat service with database."""
        self.db = db
        self.sessions_collection = db.chat_sessions

    async def create_session(
        self,
        user_id: ObjectId,
        mode: str = "solo",
        humor_style: str = "random",
    ) -> ChatSessionInDB:
        """
        Create new chat session (FR-020).

        Args:
            user_id: User's ObjectId
            mode: Session mode ("solo" or "challenge")
            humor_style: Humor style for session

        Returns:
            Created chat session
        """
        session = ChatSession(
            user_id=user_id,
            mode=mode,
            humor_style=humor_style,
            messages=[],
            is_active=True,
        )

        result = await self.sessions_collection.insert_one(
            session.model_dump(by_alias=True, exclude={"id"})
        )
        session.id = result.inserted_id

        logger.info(f"Created {mode} chat session for user {user_id}")
        return ChatSessionInDB(**session.model_dump())

    async def get_session_by_id(
        self, session_id: ObjectId, user_id: Optional[ObjectId] = None
    ) -> Optional[ChatSessionInDB]:
        """
        Get chat session by ID.

        Args:
            session_id: Session ObjectId
            user_id: Optional user_id to verify ownership

        Returns:
            Chat session if found and authorized
        """
        query = {"_id": session_id}
        if user_id:
            query["user_id"] = user_id

        session_doc = await self.sessions_collection.find_one(query)
        if not session_doc:
            return None

        return ChatSessionInDB(**session_doc)

    async def get_user_sessions(
        self, user_id: ObjectId, limit: int = 10, offset: int = 0
    ) -> tuple[list[ChatSessionInDB], int]:
        """
        Get user's chat sessions (FR-014 chat history).

        Args:
            user_id: User's ObjectId
            limit: Max number of sessions to return
            offset: Number of sessions to skip

        Returns:
            Tuple of (sessions list, total count)
        """
        query = {"user_id": user_id}

        # Get total count
        total = await self.sessions_collection.count_documents(query)

        # Get paginated sessions
        cursor = (
            self.sessions_collection.find(query)
            .sort("session_start", -1)
            .skip(offset)
            .limit(limit)
        )

        sessions = []
        async for doc in cursor:
            sessions.append(ChatSessionInDB(**doc))

        return sessions, total

    async def add_message_to_session(
        self,
        session_id: ObjectId,
        message: ChatMessage,
    ) -> bool:
        """
        Add message to chat session (FR-008, FR-013).

        Args:
            session_id: Session ObjectId
            message: ChatMessage to add

        Returns:
            True if added successfully
        """
        result = await self.sessions_collection.update_one(
            {"_id": session_id},
            {
                "$push": {"messages": message.model_dump()},
                "$inc": {"total_messages": 1},
                "$set": {"updated_at": datetime.utcnow()},
            },
        )

        return result.modified_count > 0

    async def add_prompt(
        self,
        session_id: ObjectId,
        prompt_text: str,
        moderation_status: str = "approved",
    ) -> str:
        """
        Add user prompt to session.

        Args:
            session_id: Session ObjectId
            prompt_text: User's prompt
            moderation_status: Moderation status

        Returns:
            Message ID of created prompt
        """
        message_id = str(uuid4())
        message = ChatMessage(
            message_id=message_id,
            type="prompt",
            prompt=PromptMessage(
                text=prompt_text,
                moderation_status=moderation_status,
            ),
        )

        await self.add_message_to_session(session_id, message)
        return message_id

    async def add_response(
        self,
        session_id: ObjectId,
        response_text: str,
        inference_time_ms: int,
        model: str,
        humor_style: str,
    ) -> str:
        """
        Add AI response to session.

        Args:
            session_id: Session ObjectId
            response_text: AI-generated response
            inference_time_ms: Time taken for inference
            model: Model name used
            humor_style: Humor style applied

        Returns:
            Message ID of created response
        """
        message_id = str(uuid4())
        message = ChatMessage(
            message_id=message_id,
            type="response",
            response=ResponseMessage(
                text=response_text,
                inference_time_ms=inference_time_ms,
                model=model,
                humor_style=humor_style,
            ),
        )

        await self.add_message_to_session(session_id, message)
        return message_id

    async def end_session(self, session_id: ObjectId) -> bool:
        """
        Mark session as ended.

        Args:
            session_id: Session ObjectId

        Returns:
            True if updated
        """
        result = await self.sessions_collection.update_one(
            {"_id": session_id},
            {
                "$set": {
                    "is_active": False,
                    "session_end": datetime.utcnow(),
                }
            },
        )

        return result.modified_count > 0

    async def get_session_history(
        self, session_id: ObjectId
    ) -> Optional[list[ChatMessage]]:
        """
        Get message history for session (FR-013, FR-014).

        Args:
            session_id: Session ObjectId

        Returns:
            List of chat messages if found
        """
        session = await self.get_session_by_id(session_id)
        if not session:
            return None

        return session.messages

    async def update_session_humor_style(
        self, session_id: ObjectId, humor_style: str
    ) -> bool:
        """
        Update session humor style (FR-011).

        Args:
            session_id: Session ObjectId
            humor_style: New humor style

        Returns:
            True if updated
        """
        result = await self.sessions_collection.update_one(
            {"_id": session_id},
            {"$set": {"humor_style": humor_style, "updated_at": datetime.utcnow()}},
        )

        return result.modified_count > 0
