"""OAuth service for Google and Facebook authentication."""

import logging
from datetime import datetime
from typing import Optional

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from ..models.user import SocialProvider, SocialProviders, User, UserConsent, UserInDB

logger = logging.getLogger(__name__)


class OAuthService:
    """Service for OAuth authentication (FR-001)."""

    def __init__(self, db: AsyncIOMotorDatabase):
        """Initialize OAuth service with database."""
        self.db = db
        self.users_collection = db.users

    async def create_or_update_user_from_oauth(
        self,
        provider: str,
        provider_id: str,
        email: str,
        display_name: str,
        avatar_url: Optional[str] = None,
        ip_address: Optional[str] = None,
    ) -> UserInDB:
        """
        Create or update user from OAuth provider (Google/Facebook).

        Args:
            provider: OAuth provider name ("google" or "facebook")
            provider_id: Provider's user ID
            email: User email from OAuth
            display_name: Display name from OAuth
            avatar_url: Avatar URL from OAuth
            ip_address: User's IP for consent tracking

        Returns:
            Created or updated user

        Raises:
            ValueError: If provider not supported
        """
        if provider not in ["google", "facebook"]:
            raise ValueError(f"Unsupported OAuth provider: {provider}")

        # Check if user exists with this provider
        query = {f"social_providers.{provider}.provider_id": provider_id}
        existing_user = await self.users_collection.find_one(query)

        if existing_user:
            # Update existing user
            logger.info(f"Updating existing user from {provider} OAuth")

            update_data = {
                "last_login": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
            }

            # Update avatar if provided
            if avatar_url:
                update_data["avatar_url"] = avatar_url

            await self.users_collection.update_one(
                {"_id": existing_user["_id"]}, {"$set": update_data}
            )

            return UserInDB(**{**existing_user, **update_data})

        # Check if user exists with same email (link accounts)
        existing_email_user = await self.users_collection.find_one({"email": email})

        if existing_email_user:
            # Link OAuth to existing email account
            logger.info(f"Linking {provider} OAuth to existing email account")

            provider_data = SocialProvider(
                provider_id=provider_id, email=email, verified=True
            )

            update_data = {
                f"social_providers.{provider}": provider_data.model_dump(),
                "last_login": datetime.utcnow(),
                "updated_at": datetime.utcnow(),
            }

            if avatar_url and not existing_email_user.get("avatar_url"):
                update_data["avatar_url"] = avatar_url

            await self.users_collection.update_one(
                {"_id": existing_email_user["_id"]}, {"$set": update_data}
            )

            updated_user = await self.users_collection.find_one(
                {"_id": existing_email_user["_id"]}
            )
            return UserInDB(**updated_user)

        # Create new user (OAuth signup bypasses email verification per data-model.md)
        logger.info(f"Creating new user from {provider} OAuth")

        social_providers = SocialProviders()
        if provider == "google":
            social_providers.google = SocialProvider(
                provider_id=provider_id, email=email, verified=True
            )
        elif provider == "facebook":
            social_providers.facebook = SocialProvider(
                provider_id=provider_id, email=email, verified=True
            )

        user = User(
            email=email,
            password_hash=None,  # No password for OAuth-only users
            social_providers=social_providers,
            display_name=display_name,
            avatar_url=avatar_url,
            email_verified=True,  # OAuth providers verify email
            consent=UserConsent(
                given=True,  # OAuth implies consent
                date=datetime.utcnow(),
                ip_address=ip_address or "0.0.0.0",
            ),
        )

        result = await self.users_collection.insert_one(
            user.model_dump(by_alias=True, exclude={"id"})
        )
        user.id = result.inserted_id

        return UserInDB(**user.model_dump())

    async def get_user_by_provider(
        self, provider: str, provider_id: str
    ) -> Optional[UserInDB]:
        """
        Get user by OAuth provider ID.

        Args:
            provider: OAuth provider name
            provider_id: Provider's user ID

        Returns:
            User if found, None otherwise
        """
        query = {f"social_providers.{provider}.provider_id": provider_id}
        user_doc = await self.users_collection.find_one(query)

        if not user_doc:
            return None

        return UserInDB(**user_doc)

    async def unlink_provider(self, user_id: ObjectId, provider: str) -> bool:
        """
        Unlink OAuth provider from user account.

        Args:
            user_id: User's ObjectId
            provider: OAuth provider to unlink

        Returns:
            True if unlinked, False if error or user has no other auth method
        """
        user_doc = await self.users_collection.find_one({"_id": user_id})
        if not user_doc:
            return False

        # Check if user has other authentication method
        has_password = user_doc.get("password_hash") is not None
        has_other_provider = False

        for p in ["google", "facebook"]:
            if p != provider and user_doc.get("social_providers", {}).get(p):
                has_other_provider = True
                break

        if not has_password and not has_other_provider:
            logger.warning(
                f"Cannot unlink {provider} - user has no other auth method"
            )
            return False

        # Unlink provider
        await self.users_collection.update_one(
            {"_id": user_id},
            {
                "$unset": {f"social_providers.{provider}": ""},
                "$set": {"updated_at": datetime.utcnow()},
            },
        )

        return True
