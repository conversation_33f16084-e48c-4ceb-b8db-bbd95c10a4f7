"""Game room service for party mode functionality."""

import logging
import random
import string
from datetime import datetime, timed<PERSON>ta
from typing import Optional

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from ..models.game_room import <PERSON>Room, GameRoomInDB, Participant, Vote

logger = logging.getLogger(__name__)


class RoomService:
    """Service for game room management (FR-021 to FR-026, FR-043)."""

    def __init__(self, db: AsyncIOMotorDatabase):
        """Initialize room service with database."""
        self.db = db
        self.rooms_collection = db.game_rooms

    def _generate_room_code(self) -> str:
        """
        Generate unique 6-character room code (FR-022).

        Returns:
            6-character alphanumeric code
        """
        return ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))

    async def create_room(
        self,
        creator_user_id: ObjectId,
        creator_display_name: str,
    ) -> GameRoomInDB:
        """
        Create new game room (FR-021).

        Args:
            creator_user_id: Room creator's ObjectId
            creator_display_name: Creator's display name

        Returns:
            Created game room
        """
        # Generate unique room code
        room_code = self._generate_room_code()

        # Ensure uniqueness
        while await self.rooms_collection.find_one({"room_code": room_code}):
            room_code = self._generate_room_code()

        # Create room with creator as first participant
        creator = Participant(
            user_id=creator_user_id,
            display_name=creator_display_name,
        )

        room = GameRoom(
            room_code=room_code,
            creator_user_id=creator_user_id,
            participants=[creator],
        )

        result = await self.rooms_collection.insert_one(
            room.model_dump(by_alias=True, exclude={"id"})
        )
        room.id = result.inserted_id

        logger.info(f"Created room {room_code} for user {creator_user_id}")
        return GameRoomInDB(**room.model_dump())

    async def get_room_by_code(self, room_code: str) -> Optional[GameRoomInDB]:
        """
        Get room by code (FR-024).

        Args:
            room_code: 6-character room code

        Returns:
            Game room if found
        """
        room_doc = await self.rooms_collection.find_one({"room_code": room_code.upper()})
        if not room_doc:
            return None

        return GameRoomInDB(**room_doc)

    async def add_participant(
        self,
        room_code: str,
        user_id: ObjectId,
        display_name: str,
    ) -> bool:
        """
        Add participant to room (FR-023).

        Args:
            room_code: Room code
            user_id: User's ObjectId
            display_name: User's display name

        Returns:
            True if added successfully

        Raises:
            ValueError: If room full (FR-043: max 4 participants)
        """
        room = await self.get_room_by_code(room_code)
        if not room:
            raise ValueError(f"Room {room_code} not found")

        if len(room.participants) >= 4:
            raise ValueError("Room full (maximum 4 participants)")

        # Check if user already in room
        if any(p.user_id == user_id for p in room.participants):
            logger.info(f"User {user_id} already in room {room_code}")
            return True

        participant = Participant(
            user_id=user_id,
            display_name=display_name,
        )

        result = await self.rooms_collection.update_one(
            {"room_code": room_code},
            {
                "$push": {"participants": participant.model_dump()},
                "$set": {
                    "status": "active" if len(room.participants) >= 1 else "waiting",
                    "started_at": datetime.utcnow() if not room.started_at else room.started_at,
                },
            },
        )

        logger.info(f"Added user {user_id} to room {room_code}")
        return result.modified_count > 0

    async def remove_participant(
        self, room_code: str, user_id: ObjectId
    ) -> bool:
        """
        Remove participant from room.

        Args:
            room_code: Room code
            user_id: User's ObjectId

        Returns:
            True if removed
        """
        result = await self.rooms_collection.update_one(
            {"room_code": room_code},
            {"$pull": {"participants": {"user_id": user_id}}},
        )

        logger.info(f"Removed user {user_id} from room {room_code}")
        return result.modified_count > 0

    async def update_participant_connection(
        self, room_code: str, user_id: ObjectId, is_connected: bool
    ) -> bool:
        """
        Update participant connection status.

        Args:
            room_code: Room code
            user_id: User's ObjectId
            is_connected: Connection status

        Returns:
            True if updated
        """
        result = await self.rooms_collection.update_one(
            {
                "room_code": room_code,
                "participants.user_id": user_id,
            },
            {"$set": {"participants.$.is_connected": is_connected}},
        )

        return result.modified_count > 0

    async def cast_vote(
        self,
        room_code: str,
        user_id: ObjectId,
        response_id: str,
    ) -> bool:
        """
        Cast vote for response (FR-025).

        Args:
            room_code: Room code
            user_id: Voter's ObjectId
            response_id: Response to vote for

        Returns:
            True if vote cast successfully
        """
        vote = Vote(user_id=user_id)

        # Add vote and increment vote count
        result = await self.rooms_collection.update_one(
            {
                "room_code": room_code,
                "rounds.responses.response_id": response_id,
            },
            {
                "$push": {"rounds.$[].responses.$[resp].votes": vote.model_dump()},
                "$inc": {"rounds.$[].responses.$[resp].vote_count": 1},
            },
            array_filters=[{"resp.response_id": response_id}],
        )

        logger.info(f"User {user_id} voted for response {response_id} in room {room_code}")
        return result.modified_count > 0

    async def calculate_top_responses(
        self, room_code: str, limit: int = 3
    ) -> list[dict]:
        """
        Calculate top voted responses (FR-026).

        Args:
            room_code: Room code
            limit: Number of top responses (default 3)

        Returns:
            List of top responses sorted by vote count
        """
        room = await self.get_room_by_code(room_code)
        if not room:
            return []

        all_responses = []
        for round_data in room.rounds:
            for response in round_data.responses:
                all_responses.append({
                    "response_id": response.response_id,
                    "response_text": response.response_text,
                    "vote_count": response.vote_count,
                    "round_number": round_data.round_number,
                })

        # Sort by vote count descending
        all_responses.sort(key=lambda x: x["vote_count"], reverse=True)

        return all_responses[:limit]

    async def end_session(self, room_code: str) -> bool:
        """
        End game session and calculate final top responses.

        Args:
            room_code: Room code

        Returns:
            True if ended successfully
        """
        # Calculate top 3 responses
        top_responses = await self.calculate_top_responses(room_code, limit=3)

        result = await self.rooms_collection.update_one(
            {"room_code": room_code},
            {
                "$set": {
                    "status": "completed",
                    "completed_at": datetime.utcnow(),
                    "top_responses": top_responses,
                }
            },
        )

        logger.info(f"Ended session for room {room_code}")
        return result.modified_count > 0

    async def cleanup_expired_rooms(self) -> int:
        """
        Clean up expired rooms (24-hour TTL).

        Returns:
            Number of rooms cleaned up
        """
        # TTL index handles automatic deletion, but we can also manually clean
        expiry_time = datetime.utcnow() - timedelta(hours=24)

        result = await self.rooms_collection.delete_many(
            {
                "status": {"$ne": "active"},
                "created_at": {"$lt": expiry_time},
            }
        )

        if result.deleted_count > 0:
            logger.info(f"Cleaned up {result.deleted_count} expired rooms")

        return result.deleted_count
