"""Daily challenge service for streak tracking and theme unlocking."""

import logging
from datetime import datetime, timedelta
from typing import Optional

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from ..models.daily_challenge import DailyChallenge, DailyChallengeInDB

logger = logging.getLogger(__name__)


class ChallengeService:
    """Service for daily challenges (FR-033, FR-034, FR-019)."""

    def __init__(self, db: AsyncIOMotorDatabase):
        """Initialize challenge service with database."""
        self.db = db
        self.challenges_collection = db.daily_challenges
        self.users_collection = db.users

    async def get_today_challenge(self) -> Optional[DailyChallengeInDB]:
        """
        Get today's daily challenge (FR-033).

        Returns:
            Today's challenge if exists
        """
        today_id = datetime.utcnow().strftime("%Y%m%d")

        challenge_doc = await self.challenges_collection.find_one(
            {"challenge_id": today_id}
        )

        if not challenge_doc:
            # Auto-generate if not exists (in production, use background job)
            return await self.generate_daily_challenge()

        return DailyChallengeInDB(**challenge_doc)

    async def generate_daily_challenge(
        self,
        date: Optional[datetime] = None,
    ) -> DailyChallengeInDB:
        """
        Generate daily challenge (scheduled job at midnight UTC).

        Args:
            date: Challenge date (defaults to today)

        Returns:
            Generated challenge
        """
        if date is None:
            date = datetime.utcnow()

        challenge_id = date.strftime("%Y%m%d")

        # Check if already exists
        existing = await self.challenges_collection.find_one(
            {"challenge_id": challenge_id}
        )
        if existing:
            return DailyChallengeInDB(**existing)

        # Generate challenge from prompt library
        # TODO: Integrate with prompt library service to get themed question
        challenge = DailyChallenge(
            challenge_id=challenge_id,
            question_text="What is the speed of light?",  # Placeholder
            theme_category="science",
            date=date.replace(hour=0, minute=0, second=0, microsecond=0),
        )

        result = await self.challenges_collection.insert_one(
            challenge.model_dump(by_alias=True, exclude={"id"})
        )
        challenge.id = result.inserted_id

        logger.info(f"Generated daily challenge {challenge_id}")
        return DailyChallengeInDB(**challenge.model_dump())

    async def complete_challenge(
        self,
        user_id: ObjectId,
        challenge_id: str,
    ) -> dict:
        """
        Mark challenge as completed and update streak (FR-034).

        Args:
            user_id: User's ObjectId
            challenge_id: Challenge ID (YYYYMMDD)

        Returns:
            Dict with streak_count and unlocked_themes
        """
        # Get user
        user = await self.users_collection.find_one({"_id": user_id})
        if not user:
            raise ValueError("User not found")

        current_streak = user.get("streak_count", 0)
        last_challenge_date = user.get("last_daily_challenge")

        # Calculate new streak
        challenge_date = datetime.strptime(challenge_id, "%Y%m%d")
        new_streak = current_streak

        if last_challenge_date:
            # Check if consecutive day
            days_diff = (challenge_date - last_challenge_date).days

            if days_diff == 1:
                # Consecutive day - increment streak
                new_streak = current_streak + 1
            elif days_diff > 1:
                # Streak broken - reset to 1
                new_streak = 1
            else:
                # Same day or past day - no change
                new_streak = current_streak
        else:
            # First challenge
            new_streak = 1

        # Check for theme unlocks (FR-019)
        unlocked_themes = await self._check_theme_unlocks(user, new_streak)

        # Update user
        update_data = {
            "streak_count": new_streak,
            "last_daily_challenge": challenge_date,
            "updated_at": datetime.utcnow(),
        }

        if unlocked_themes:
            update_data["$push"] = {"unlocked_themes": {"$each": unlocked_themes}}

        await self.users_collection.update_one(
            {"_id": user_id},
            {"$set": update_data} if not unlocked_themes else {"$set": {k: v for k, v in update_data.items() if k != "$push"}, "$push": update_data["$push"]},
        )

        # Increment challenge completion count
        await self.challenges_collection.update_one(
            {"challenge_id": challenge_id},
            {"$inc": {"completion_count": 1}},
        )

        logger.info(
            f"User {user_id} completed challenge {challenge_id}. Streak: {new_streak}"
        )

        return {
            "streak_count": new_streak,
            "unlocked_themes": unlocked_themes,
        }

    async def _check_theme_unlocks(
        self, user: dict, new_streak: int
    ) -> list[str]:
        """
        Check if user unlocked new themes based on streak (FR-019).

        Args:
            user: User document
            new_streak: New streak count

        Returns:
            List of newly unlocked theme IDs
        """
        unlocked = user.get("unlocked_themes", [])
        newly_unlocked = []

        # Theme unlock criteria (from data-model.md)
        unlock_criteria = {
            "space-week": 7,  # 7-day streak
            # Add more themed collections
        }

        for theme_id, required_streak in unlock_criteria.items():
            if theme_id not in unlocked and new_streak >= required_streak:
                newly_unlocked.append(theme_id)
                logger.info(f"User unlocked theme: {theme_id}")

        return newly_unlocked

    async def get_user_streak(self, user_id: ObjectId) -> dict:
        """
        Get user's current streak information.

        Args:
            user_id: User's ObjectId

        Returns:
            Dict with streak info
        """
        user = await self.users_collection.find_one(
            {"_id": user_id},
            {"streak_count": 1, "last_daily_challenge": 1},
        )

        if not user:
            return {"streak_count": 0, "last_challenge_date": None}

        return {
            "streak_count": user.get("streak_count", 0),
            "last_challenge_date": user.get("last_daily_challenge"),
        }
