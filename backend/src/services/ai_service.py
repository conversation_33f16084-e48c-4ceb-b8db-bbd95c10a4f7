"""AI service for generating humorous wrong answers using llama.cpp and PydanticAI."""

import asyncio
import time
from typing import Op<PERSON>

from llama_cpp import <PERSON>lama
from pydantic import BaseModel, Field

from ..core.logging import get_logger

logger = get_logger(__name__)


class HumorResponseSchema(BaseModel):
    """Pydantic schema for AI humor response validation."""

    answer: str = Field(description="The humorous wrong answer")
    confidence: float = Field(
        ge=0.0, le=1.0, description="Confidence level of the answer"
    )
    style: str = Field(
        description="Humor style used",
        pattern="^(absurd|sarcastic|whimsical|childlike|random)$",
    )


class AIService:
    """Service for AI model inference with humor generation."""

    def __init__(self, model_path: str, n_ctx: int = 512, n_threads: int = 4):
        """Initialize AI service with llama.cpp model."""
        self.model_path = model_path
        self.n_ctx = n_ctx
        self.n_threads = n_threads
        self.llm: Optional[Llama] = None
        self.humor_prompts = {
            "absurd": "Give a completely absurd and nonsensical answer that defies logic.",
            "sarcastic": "Give a sarcastic answer with a witty, ironic twist.",
            "whimsical": "Give a whimsical, playful answer full of imagination.",
            "childlike": "Give a childlike, innocent answer with simple and cute logic.",
            "random": "Give a funny wrong answer in any style you choose.",
        }

    async def load_model(self) -> None:
        """Load llama.cpp model at startup."""
        logger.info(f"Loading AI model from {self.model_path}")
        try:
            # Run model loading in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            self.llm = await loop.run_in_executor(
                None,
                lambda: Llama(
                    model_path=self.model_path,
                    n_ctx=self.n_ctx,
                    n_threads=self.n_threads,
                    n_gpu_layers=0,  # CPU only for now
                    verbose=False,
                ),
            )
            logger.info("AI model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load AI model: {e}")
            raise

    async def generate_wrong_answer(
        self,
        prompt: str,
        humor_style: str = "random",
        max_tokens: int = 50,
        timeout: float = 15.0,
    ) -> HumorResponseSchema:
        """
        Generate a humorous wrong answer for the given prompt.

        Args:
            prompt: User's question
            humor_style: Style of humor to use
            max_tokens: Maximum tokens to generate
            timeout: Maximum time for inference (FR-010: <2s)

        Returns:
            HumorResponseSchema with answer, confidence, and style

        Raises:
            TimeoutError: If inference exceeds timeout
            RuntimeError: If model not loaded
        """
        if not self.llm:
            raise RuntimeError("AI model not loaded. Call load_model() first.")

        humor_instruction = self.humor_prompts.get(
            humor_style, self.humor_prompts["random"]
        )

        system_prompt = f"""You are a funny AI that gives intentionally wrong but hilarious answers.
{humor_instruction}
Always give a confident but completely incorrect answer.
Keep it family-friendly and entertaining."""

        full_prompt = f"""<|system|>
{system_prompt}
<|user|>
{prompt}
<|assistant|>
"""

        start_time = time.time()

        try:
            # Run inference in thread pool with timeout
            loop = asyncio.get_event_loop()
            response = await asyncio.wait_for(
                loop.run_in_executor(
                    None,
                    lambda: self.llm.create_completion(
                        full_prompt,
                        max_tokens=max_tokens,
                        temperature=0.9,
                        top_p=0.95,
                        top_k=40,
                        repeat_penalty=1.1,
                        stop=["<|user|>", "<|system|>", "\n\n"],
                    ),
                ),
                timeout=timeout,
            )

            inference_time = (time.time() - start_time) * 1000  # Convert to ms

            # Extract generated text
            answer_text = response["choices"][0]["text"].strip()

            logger.info(
                f"Generated answer in {inference_time:.2f}ms with style {humor_style}"
            )

            return HumorResponseSchema(
                answer=answer_text,
                confidence=0.95,  # Always confident in being wrong!
                style=humor_style,
            )

        except asyncio.TimeoutError:
            logger.warning(f"AI inference timeout after {timeout}s")
            # Fallback response per FR-044
            return HumorResponseSchema(
                answer="My brain had a funny moment! Let me think of something silly... 🤔",
                confidence=0.1,
                style=humor_style,
            )
        except Exception as e:
            logger.error(f"AI inference error: {e}")
            return HumorResponseSchema(
                answer="Oops! My circuits got tangled. Try asking again!",
                confidence=0.0,
                style=humor_style,
            )

    async def refine_response(
        self, original_answer: str, refinement_instruction: str, humor_style: str
    ) -> str:
        """
        Refine an existing answer based on user instruction (FR-015).

        Args:
            original_answer: The previous AI response
            refinement_instruction: User instruction like "make it sillier" or "keep it short"
            humor_style: Style to maintain

        Returns:
            Refined answer text
        """
        if not self.llm:
            raise RuntimeError("AI model not loaded")

        refine_prompt = f"""<|system|>
You are refining a humorous wrong answer based on user feedback.
Original answer: {original_answer}
User wants: {refinement_instruction}
Maintain the {humor_style} humor style.
<|user|>
Refine the answer according to the instruction.
<|assistant|>
"""

        try:
            loop = asyncio.get_event_loop()
            response = await asyncio.wait_for(
                loop.run_in_executor(
                    None,
                    lambda: self.llm.create_completion(
                        refine_prompt,
                        max_tokens=150,
                        temperature=0.8,
                        stop=["<|user|>"],
                    ),
                ),
                timeout=2.0,
            )

            refined_text = response["choices"][0]["text"].strip()
            return refined_text

        except Exception as e:
            logger.error(f"Refinement error: {e}")
            # Return original if refinement fails
            return original_answer

    def unload_model(self) -> None:
        """Unload model and free memory."""
        if self.llm:
            logger.info("Unloading AI model")
            self.llm = None


# Singleton instance
_ai_service: Optional[AIService] = None


def get_ai_service() -> AIService:
    """Get singleton AI service instance."""
    global _ai_service
    if _ai_service is None:
        raise RuntimeError("AI service not initialized")
    return _ai_service


async def initialize_ai_service(model_path: str) -> AIService:
    """Initialize and load AI service."""
    global _ai_service
    _ai_service = AIService(model_path)
    await _ai_service.load_model()
    return _ai_service
