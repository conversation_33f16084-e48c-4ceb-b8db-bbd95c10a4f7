"""User service for profile management and GDPR compliance."""

import logging
from datetime import datetime
from typing import Optional

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

from ..models.user import UserInDB, UserPreferences

logger = logging.getLogger(__name__)


class UserService:
    """Service for user profile management (FR-007, FR-041)."""

    def __init__(self, db: AsyncIOMotorDatabase):
        """Initialize user service with database."""
        self.db = db
        self.users_collection = db.users
        self.sessions_collection = db.chat_sessions
        self.rooms_collection = db.game_rooms
        self.leaderboard_collection = db.leaderboard_cache

    async def get_user_profile(self, user_id: ObjectId) -> Optional[UserInDB]:
        """
        Get user profile (FR-007).

        Args:
            user_id: User's ObjectId

        Returns:
            User profile if found
        """
        user_doc = await self.users_collection.find_one({"_id": user_id})
        if not user_doc:
            return None

        return UserInDB(**user_doc)

    async def update_profile(
        self,
        user_id: ObjectId,
        display_name: Optional[str] = None,
        avatar_url: Optional[str] = None,
        preferences: Optional[UserPreferences] = None,
    ) -> Optional[UserInDB]:
        """
        Update user profile (FR-007).

        Args:
            user_id: User's ObjectId
            display_name: New display name
            avatar_url: New avatar URL
            preferences: Updated preferences

        Returns:
            Updated user profile
        """
        update_data = {"updated_at": datetime.utcnow()}

        if display_name:
            update_data["display_name"] = display_name

        if avatar_url:
            update_data["avatar_url"] = avatar_url

        if preferences:
            update_data["preferences"] = preferences.model_dump()

        result = await self.users_collection.update_one(
            {"_id": user_id},
            {"$set": update_data},
        )

        if result.modified_count == 0:
            return None

        return await self.get_user_profile(user_id)

    async def delete_user_cascade(self, user_id: ObjectId) -> bool:
        """
        Delete user account with cascade (FR-041 GDPR/LGPD compliance).

        Deletes:
        1. User document
        2. All chat sessions
        3. Removes from game rooms
        4. Removes from leaderboard cache

        Args:
            user_id: User's ObjectId

        Returns:
            True if deleted successfully
        """
        logger.info(f"Starting cascade deletion for user {user_id}")

        # 1. Delete all chat sessions
        sessions_result = await self.sessions_collection.delete_many(
            {"user_id": user_id}
        )
        logger.info(f"Deleted {sessions_result.deleted_count} chat sessions")

        # 2. Remove user from game room participants
        rooms_result = await self.rooms_collection.update_many(
            {"participants.user_id": user_id},
            {"$pull": {"participants": {"user_id": user_id}}},
        )
        logger.info(f"Removed from {rooms_result.modified_count} game rooms")

        # 3. Anonymize leaderboard entries (keep response, remove user attribution)
        leaderboard_result = await self.leaderboard_collection.update_many(
            {"entries.user_id": str(user_id)},
            {
                "$set": {
                    "entries.$[elem].user_display_name": "[Deleted User]",
                }
            },
            array_filters=[{"elem.user_id": str(user_id)}],
        )
        logger.info(f"Anonymized {leaderboard_result.modified_count} leaderboard entries")

        # 4. Delete user document
        user_result = await self.users_collection.delete_one({"_id": user_id})

        if user_result.deleted_count == 0:
            logger.error(f"Failed to delete user {user_id}")
            return False

        logger.info(f"Successfully deleted user {user_id} with cascade")
        return True

    async def get_user_stats(self, user_id: ObjectId) -> dict:
        """
        Get user engagement statistics.

        Args:
            user_id: User's ObjectId

        Returns:
            Dict with user stats
        """
        user = await self.get_user_profile(user_id)
        if not user:
            return {}

        # Calculate additional stats
        total_sessions = await self.sessions_collection.count_documents(
            {"user_id": user_id}
        )

        active_sessions = await self.sessions_collection.count_documents(
            {"user_id": user_id, "is_active": True}
        )

        return {
            **user.stats.model_dump(),
            "total_sessions": total_sessions,
            "active_sessions": active_sessions,
            "streak_count": user.streak_count,
            "unlocked_themes_count": len(user.unlocked_themes),
            "favorites_count": len(user.favorite_response_ids),
        }

    async def increment_stats(self, user_id: ObjectId, stat_name: str, amount: int = 1) -> bool:
        """
        Increment a user stat counter.

        Args:
            user_id: User's ObjectId
            stat_name: Name of the stat to increment (e.g., 'total_prompts', 'total_shares')
            amount: Amount to increment by (default: 1)

        Returns:
            True if incremented successfully
        """
        result = await self.users_collection.update_one(
            {"_id": user_id},
            {
                "$inc": {f"stats.{stat_name}": amount},
                "$set": {"updated_at": datetime.utcnow()}
            },
        )

        return result.modified_count > 0

    async def mark_user_for_deletion(
        self, user_id: ObjectId, grace_period_days: int = 30
    ) -> bool:
        """
        Mark user for deletion (inactive account cleanup).

        Args:
            user_id: User's ObjectId
            grace_period_days: Grace period before deletion

        Returns:
            True if marked successfully
        """
        from datetime import timedelta

        deletion_date = datetime.utcnow() + timedelta(days=grace_period_days)

        result = await self.users_collection.update_one(
            {"_id": user_id},
            {
                "$set": {
                    "marked_for_deletion": True,
                    "deletion_date": deletion_date,
                    "updated_at": datetime.utcnow(),
                }
            },
        )

        logger.info(
            f"Marked user {user_id} for deletion on {deletion_date.isoformat()}"
        )
        return result.modified_count > 0

    async def process_deletion_queue(self) -> int:
        """
        Process users marked for deletion (background job).

        Returns:
            Number of users deleted
        """
        # Find users past deletion date
        users_to_delete = []
        async for user in self.users_collection.find(
            {
                "marked_for_deletion": True,
                "deletion_date": {"$lte": datetime.utcnow()},
            }
        ):
            users_to_delete.append(user["_id"])

        deleted_count = 0
        for user_id in users_to_delete:
            if await self.delete_user_cascade(user_id):
                deleted_count += 1

        if deleted_count > 0:
            logger.info(f"Processed deletion queue: {deleted_count} users deleted")

        return deleted_count
