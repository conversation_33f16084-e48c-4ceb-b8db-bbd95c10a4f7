"""Content moderation service for filtering prompts and AI outputs."""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

logger = logging.getLogger(__name__)


class ModerationService:
    """Service for content moderation (FR-037, FR-038, FR-039, FR-040)."""

    def __init__(
        self,
        db: AsyncIOMotorDatabase,
        profanity_list_path: Optional[str] = None,
    ):
        """
        Initialize moderation service.

        Args:
            db: MongoDB database
            profanity_list_path: Path to profanity keywords JSON file
        """
        self.db = db
        self.flagged_content_collection = db.flagged_content
        self.profanity_keywords = self._load_profanity_list(profanity_list_path)

    def _load_profanity_list(self, path: Optional[str] = None) -> set[str]:
        """
        Load profanity keywords from JSON file.

        Args:
            path: Path to profanity list JSON

        Returns:
            Set of lowercase profanity keywords
        """
        if not path or not Path(path).exists():
            # Default profanity list (basic examples, expand in production)
            logger.warning("Using default profanity list. Load from file in production.")
            return {
                "offensive1",
                "offensive2",
                "slur1",
                "slur2",
                "inappropriate1",
                # Add comprehensive list in production
            }

        try:
            with open(path, "r") as f:
                data = json.load(f)
                return set(word.lower() for word in data.get("keywords", []))
        except Exception as e:
            logger.error(f"Failed to load profanity list: {e}")
            return set()

    async def check_prompt_safety(
        self, prompt_text: str, user_id: ObjectId, session_id: Optional[str] = None
    ) -> tuple[str, list[str]]:
        """
        Check if prompt contains inappropriate content (FR-037).

        Args:
            prompt_text: User's prompt text
            user_id: User who submitted the prompt
            session_id: Optional session ID for logging

        Returns:
            Tuple of (moderation_status, matched_keywords)
            - status: "approved", "flagged", or "blocked"
            - matched_keywords: List of matched profanity keywords
        """
        if not prompt_text or not prompt_text.strip():
            return "approved", []

        # Check for profanity keywords
        prompt_lower = prompt_text.lower()
        matched = [
            keyword for keyword in self.profanity_keywords if keyword in prompt_lower
        ]

        if not matched:
            return "approved", []

        # Determine severity (for MVP, any match is flagged)
        # In production, could have severity levels
        status = "flagged" if len(matched) <= 2 else "blocked"

        # Log for admin review (FR-040)
        await self._flag_for_review(
            user_id=user_id,
            prompt_text=prompt_text,
            matched_keywords=matched,
            session_id=session_id,
        )

        logger.warning(
            f"Content moderation: {status} - matched {len(matched)} keywords"
        )

        return status, matched

    async def validate_response_safety(self, response_text: str) -> bool:
        """
        Validate AI response doesn't contain inappropriate content (FR-038).

        Args:
            response_text: AI-generated response

        Returns:
            True if safe, False if contains inappropriate content
        """
        if not response_text:
            return True

        response_lower = response_text.lower()
        matched = [
            keyword for keyword in self.profanity_keywords if keyword in response_lower
        ]

        if matched:
            logger.warning(
                f"AI output validation failed: matched {len(matched)} keywords"
            )
            return False

        return True

    async def _flag_for_review(
        self,
        user_id: ObjectId,
        prompt_text: str,
        matched_keywords: list[str],
        session_id: Optional[str] = None,
    ) -> None:
        """
        Log flagged content for admin review (FR-040).

        Args:
            user_id: User who submitted content
            prompt_text: Flagged prompt
            matched_keywords: Keywords that matched
            session_id: Optional session ID
        """
        flagged_doc = {
            "user_id": user_id,
            "prompt_text": prompt_text,
            "matched_keywords": matched_keywords,
            "session_id": session_id,
            "timestamp": datetime.utcnow(),
            "reviewed": False,
            "action_taken": None,
        }

        await self.flagged_content_collection.insert_one(flagged_doc)
        logger.info(f"Flagged content logged for user {user_id}")

    async def get_flagged_content(
        self, skip: int = 0, limit: int = 50, reviewed: Optional[bool] = None
    ) -> list[dict]:
        """
        Get flagged content for admin review (FR-040).

        Args:
            skip: Number of records to skip (pagination)
            limit: Max number of records to return
            reviewed: Filter by reviewed status (None = all)

        Returns:
            List of flagged content documents
        """
        query = {}
        if reviewed is not None:
            query["reviewed"] = reviewed

        cursor = (
            self.flagged_content_collection.find(query)
            .sort("timestamp", -1)
            .skip(skip)
            .limit(limit)
        )

        return await cursor.to_list(length=limit)

    async def mark_content_reviewed(
        self, flagged_id: ObjectId, action_taken: str
    ) -> bool:
        """
        Mark flagged content as reviewed by admin.

        Args:
            flagged_id: Flagged content document ID
            action_taken: Action taken by admin (e.g., "dismissed", "banned_user")

        Returns:
            True if updated successfully
        """
        result = await self.flagged_content_collection.update_one(
            {"_id": flagged_id},
            {
                "$set": {
                    "reviewed": True,
                    "action_taken": action_taken,
                    "reviewed_at": datetime.utcnow(),
                }
            },
        )

        return result.modified_count > 0

    async def flag_for_review(
        self,
        user_id: ObjectId,
        content: str,
        matched_keywords: list[str],
        content_type: str = "prompt",
        session_id: Optional[str] = None,
    ) -> None:
        """
        Public method to flag content for admin review (FR-040).

        Args:
            user_id: User who submitted content
            content: Flagged content text
            matched_keywords: Keywords that matched
            content_type: Type of content ("prompt", "response", etc.)
            session_id: Optional session ID
        """
        return await self._flag_for_review(
            user_id=user_id,
            prompt_text=content,  # Using prompt_text for backward compatibility
            matched_keywords=matched_keywords,
            session_id=session_id,
        )

    def get_friendly_error_message(self, moderation_status: str) -> str:
        """
        Get friendly error message for moderated content (FR-039).

        Args:
            moderation_status: Status from check_prompt_safety

        Returns:
            User-friendly error message
        """
        if moderation_status == "blocked":
            return "Let's keep it fun and friendly! Try a different question."
        elif moderation_status == "flagged":
            return "Let's keep it fun and friendly! Try a different question."
        else:
            return "Ask me anything! I promise to get it hilariously wrong."
