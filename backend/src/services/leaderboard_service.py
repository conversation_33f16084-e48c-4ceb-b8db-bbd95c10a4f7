"""Leaderboard service for global rankings."""

import logging
from datetime import datetime, timed<PERSON>ta
from typing import Optional

from motor.motor_asyncio import AsyncIOMotorDatabase

from ..models.leaderboard_cache import LeaderboardCache, LeaderboardEntry

logger = logging.getLogger(__name__)


class LeaderboardService:
    """Service for leaderboard management (FR-036)."""

    def __init__(self, db: AsyncIOMotorDatabase):
        """Initialize leaderboard service with database."""
        self.db = db
        self.cache_collection = db.leaderboard_cache
        self.sessions_collection = db.chat_sessions
        self.rooms_collection = db.game_rooms
        self.users_collection = db.users

    async def get_cached_leaderboard(
        self, cache_type: str = "most_liked"
    ) -> Optional[LeaderboardCache]:
        """
        Get cached leaderboard (FR-036).

        Args:
            cache_type: Type of leaderboard ("most_liked" or "most_shared")

        Returns:
            Cached leaderboard if fresh, None if expired
        """
        cache_doc = await self.cache_collection.find_one(
            {
                "_id": f"global_{cache_type}",
                "expires_at": {"$gt": datetime.utcnow()},
            }
        )

        if not cache_doc:
            # Cache expired or doesn't exist - regenerate
            return await self.regenerate_leaderboard(cache_type)

        return LeaderboardCache(**cache_doc)

    async def regenerate_leaderboard(
        self, cache_type: str = "most_liked", limit: int = 100
    ) -> LeaderboardCache:
        """
        Regenerate leaderboard using aggregation pipeline.

        Args:
            cache_type: Type of leaderboard
            limit: Max entries (default 100)

        Returns:
            Regenerated and cached leaderboard
        """
        entries = []

        if cache_type == "most_liked":
            entries = await self._aggregate_most_voted()
        elif cache_type == "most_shared":
            entries = await self._aggregate_most_shared()

        # Sort and limit
        entries.sort(key=lambda x: x.vote_count + x.share_count, reverse=True)
        entries = entries[:limit]

        # Create cache document
        cache = LeaderboardCache(
            id=f"global_{cache_type}",
            cache_type=cache_type,
            entries=entries,
        )

        # Upsert to cache collection
        await self.cache_collection.update_one(
            {"_id": cache.id},
            {"$set": cache.model_dump(by_alias=True)},
            upsert=True,
        )

        logger.info(f"Regenerated {cache_type} leaderboard with {len(entries)} entries")
        return cache

    async def _aggregate_most_voted(self) -> list[LeaderboardEntry]:
        """
        Aggregate most-voted responses from game rooms.

        Returns:
            List of leaderboard entries
        """
        entries = []

        # Aggregate from game rooms
        pipeline = [
            {"$unwind": "$rounds"},
            {"$unwind": "$rounds.responses"},
            {
                "$project": {
                    "response_id": "$rounds.responses.response_id",
                    "response_text": "$rounds.responses.response_text",
                    "prompt_text": "$rounds.responses.prompt_text",
                    "vote_count": "$rounds.responses.vote_count",
                    "timestamp": "$rounds.responses.timestamp",
                }
            },
            {"$sort": {"vote_count": -1}},
            {"$limit": 100},
        ]

        async for doc in self.rooms_collection.aggregate(pipeline):
            # Get user display name (simplified - in production, join with users)
            entries.append(
                LeaderboardEntry(
                    response_id=doc["response_id"],
                    response_text=doc["response_text"],
                    prompt_text=doc.get("prompt_text", ""),
                    user_display_name="Anonymous",  # TODO: Join with users
                    vote_count=doc["vote_count"],
                    share_count=0,
                    timestamp=doc["timestamp"],
                )
            )

        return entries

    async def _aggregate_most_shared(self) -> list[LeaderboardEntry]:
        """
        Aggregate most-shared responses from sessions and rooms.

        Returns:
            List of leaderboard entries
        """
        entries = []

        # Aggregate from chat sessions
        pipeline = [
            {"$unwind": "$messages"},
            {"$match": {"messages.type": "response"}},
            {
                "$project": {
                    "response_id": "$messages.message_id",
                    "response_text": "$messages.response.text",
                    "share_count": "$messages.response.share_count",
                    "vote_count": "$messages.response.vote_count",
                    "timestamp": "$messages.response.timestamp",
                    "user_id": "$user_id",
                }
            },
            {"$sort": {"share_count": -1}},
            {"$limit": 100},
        ]

        async for doc in self.sessions_collection.aggregate(pipeline):
            # Get user display name
            user = await self.users_collection.find_one(
                {"_id": doc["user_id"]},
                {"display_name": 1},
            )

            entries.append(
                LeaderboardEntry(
                    response_id=doc["response_id"],
                    response_text=doc["response_text"],
                    prompt_text="",  # TODO: Get from context
                    user_display_name=user.get("display_name", "Anonymous") if user else "Anonymous",
                    vote_count=doc.get("vote_count", 0),
                    share_count=doc["share_count"],
                    timestamp=doc["timestamp"],
                )
            )

        return entries

    async def schedule_regeneration(self) -> None:
        """
        Schedule background task to regenerate leaderboards every 5 minutes.

        This should be called from FastAPI background tasks or scheduler.
        """
        # Check if cache needs regeneration
        for cache_type in ["most_liked", "most_shared"]:
            cache = await self.cache_collection.find_one(
                {"_id": f"global_{cache_type}"}
            )

            if not cache or cache["expires_at"] <= datetime.utcnow():
                await self.regenerate_leaderboard(cache_type)
                logger.info(f"Scheduled regeneration for {cache_type} leaderboard")
