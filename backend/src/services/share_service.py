"""Share service for generating shareable links for responses."""

import hashlib
import logging
from datetime import datetime

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

logger = logging.getLogger(__name__)


class ShareService:
    """Service for sharing responses on social media (FR-030, FR-031, FR-032)."""

    def __init__(self, db: AsyncIOMotorDatabase, base_url: str = "https://hallucination-station.example.com"):
        """
        Initialize share service.

        Args:
            db: MongoDB database
            base_url: Base URL for share links
        """
        self.db = db
        self.sessions_collection = db.chat_sessions
        self.rooms_collection = db.game_rooms
        self.base_url = base_url

    async def generate_share_url(
        self,
        response_id: str,
        platform: str = "clipboard",
    ) -> dict:
        """
        Generate shareable link for response (FR-031, FR-032).

        Args:
            response_id: Response message ID
            platform: Social platform ("twitter", "facebook", "instagram", "clipboard")

        Returns:
            Dict with share_url and preview_image_url
        """
        # Find response in sessions or rooms
        response_data = await self._find_response(response_id)
        if not response_data:
            raise ValueError(f"Response {response_id} not found")

        # Generate share URL
        share_hash = self._generate_share_hash(response_id)
        share_url = f"{self.base_url}/share/{share_hash}"

        # Increment share count
        await self._increment_share_count(response_id)

        # Generate preview image URL (placeholder, integrate with image service)
        preview_image_url = await self._generate_preview_image(
            response_data["response_text"],
            response_data.get("prompt_text", ""),
        )

        # Platform-specific URLs
        platform_urls = {
            "twitter": f"https://twitter.com/intent/tweet?url={share_url}&text=Check out this hilarious AI response!",
            "facebook": f"https://www.facebook.com/sharer/sharer.php?u={share_url}",
            "instagram": share_url,  # Instagram doesn't support URL sharing directly
            "clipboard": share_url,
        }

        final_url = platform_urls.get(platform, share_url)

        logger.info(f"Generated {platform} share URL for response {response_id}")

        return {
            "share_url": final_url,
            "preview_image_url": preview_image_url,
            "platform": platform,
        }

    async def _find_response(self, response_id: str) -> dict | None:
        """
        Find response in chat sessions or game rooms.

        Args:
            response_id: Response message ID

        Returns:
            Dict with response data if found
        """
        # Search in chat sessions
        session = await self.sessions_collection.find_one(
            {"messages.message_id": response_id}
        )

        if session:
            for msg in session.get("messages", []):
                if msg.get("message_id") == response_id and msg.get("type") == "response":
                    # Find corresponding prompt
                    prompt_text = ""
                    for m in session["messages"]:
                        if m.get("type") == "prompt":
                            prompt_text = m["prompt"]["text"]
                            break

                    return {
                        "response_id": response_id,
                        "response_text": msg["response"]["text"],
                        "prompt_text": prompt_text,
                        "humor_style": msg["response"]["humor_style"],
                        "source": "chat",
                    }

        # Search in game rooms
        room = await self.rooms_collection.find_one(
            {"rounds.responses.response_id": response_id}
        )

        if room:
            for round_data in room.get("rounds", []):
                for response in round_data.get("responses", []):
                    if response.get("response_id") == response_id:
                        return {
                            "response_id": response_id,
                            "response_text": response["response_text"],
                            "prompt_text": response["prompt_text"],
                            "source": "room",
                            "room_code": room["room_code"],
                        }

        return None

    async def _increment_share_count(self, response_id: str) -> bool:
        """
        Increment share count for response.

        Args:
            response_id: Response message ID

        Returns:
            True if incremented
        """
        # Try chat sessions
        result = await self.sessions_collection.update_one(
            {"messages.message_id": response_id, "messages.type": "response"},
            {"$inc": {"messages.$.response.share_count": 1}},
        )

        if result.modified_count > 0:
            return True

        # Try game rooms
        result = await self.rooms_collection.update_one(
            {"rounds.responses.response_id": response_id},
            {"$inc": {"rounds.$[].responses.$[resp].share_count": 1}},
            array_filters=[{"resp.response_id": response_id}],
        )

        return result.modified_count > 0

    def _generate_share_hash(self, response_id: str) -> str:
        """
        Generate short hash for share URL.

        Args:
            response_id: Response message ID

        Returns:
            8-character hash
        """
        hash_obj = hashlib.md5(response_id.encode())
        return hash_obj.hexdigest()[:8]

    async def _generate_preview_image(
        self, response_text: str, prompt_text: str
    ) -> str:
        """
        Generate preview image for social sharing (FR-032).

        Args:
            response_text: AI response
            prompt_text: Original prompt

        Returns:
            URL to preview image
        """
        # TODO: Integrate with image generation service
        # For MVP, return placeholder
        # In production, use service like Puppeteer/Playwright to render:
        # - Card with gradient background
        # - Prompt text (question)
        # - Response text (answer)
        # - Branding/logo

        placeholder_url = f"{self.base_url}/api/og-image?text={response_text[:100]}"
        return placeholder_url

    async def get_share_stats(self, response_id: str) -> dict:
        """
        Get sharing statistics for response.

        Args:
            response_id: Response message ID

        Returns:
            Dict with share stats
        """
        response_data = await self._find_response(response_id)
        if not response_data:
            return {"share_count": 0, "platforms": []}

        # For MVP, return basic stats
        # In production, track per-platform shares
        return {
            "response_id": response_id,
            "share_count": response_data.get("share_count", 0),
            "platforms": [],  # TODO: Track platform-specific shares
        }
