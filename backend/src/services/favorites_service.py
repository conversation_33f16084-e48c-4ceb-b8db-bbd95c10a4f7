"""Favorites service for managing user's favorite responses."""

import logging
from datetime import datetime

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase

logger = logging.getLogger(__name__)


class FavoritesService:
    """Service for managing favorite responses (FR-035)."""

    def __init__(self, db: AsyncIOMotorDatabase):
        """Initialize favorites service with database."""
        self.db = db
        self.users_collection = db.users
        self.sessions_collection = db.chat_sessions

    async def add_to_favorites(
        self, user_id: ObjectId, response_id: str
    ) -> bool:
        """
        Add response to user's favorites (FR-035).

        Args:
            user_id: User's ObjectId
            response_id: Response message ID

        Returns:
            True if added successfully
        """
        # Add to user's favorite_response_ids array
        result = await self.users_collection.update_one(
            {"_id": user_id},
            {
                "$addToSet": {"favorite_response_ids": response_id},
                "$set": {"updated_at": datetime.utcnow()},
                "$inc": {"stats.total_favorites": 1},
            },
        )

        if result.modified_count > 0:
            # Mark response as favorited in chat session
            await self._mark_response_favorited(response_id, True)
            logger.info(f"Added response {response_id} to favorites for user {user_id}")
            return True

        return False

    async def remove_from_favorites(
        self, user_id: ObjectId, response_id: str
    ) -> bool:
        """
        Remove response from user's favorites.

        Args:
            user_id: User's ObjectId
            response_id: Response message ID

        Returns:
            True if removed successfully
        """
        result = await self.users_collection.update_one(
            {"_id": user_id},
            {
                "$pull": {"favorite_response_ids": response_id},
                "$set": {"updated_at": datetime.utcnow()},
                "$inc": {"stats.total_favorites": -1},
            },
        )

        if result.modified_count > 0:
            # Check if any other user still has this favorited
            count = await self.users_collection.count_documents(
                {"favorite_response_ids": response_id}
            )

            if count == 0:
                await self._mark_response_favorited(response_id, False)

            logger.info(f"Removed response {response_id} from favorites for user {user_id}")
            return True

        return False

    async def get_user_favorites(
        self, user_id: ObjectId, limit: int = 50, offset: int = 0
    ) -> list[dict]:
        """
        Get user's favorite responses with context.

        Args:
            user_id: User's ObjectId
            limit: Max number of favorites to return
            offset: Number to skip (pagination)

        Returns:
            List of favorite response objects with context
        """
        # Get user's favorite response IDs
        user = await self.users_collection.find_one(
            {"_id": user_id},
            {"favorite_response_ids": 1}
        )

        if not user or not user.get("favorite_response_ids"):
            return []

        favorite_ids = user["favorite_response_ids"][offset:offset + limit]

        # Find responses in chat sessions
        favorites = []
        async for session in self.sessions_collection.find(
            {"messages.message_id": {"$in": favorite_ids}}
        ):
            for msg in session.get("messages", []):
                if msg.get("message_id") in favorite_ids and msg.get("type") == "response":
                    favorites.append({
                        "response_id": msg["message_id"],
                        "response_text": msg["response"]["text"],
                        "humor_style": msg["response"]["humor_style"],
                        "session_id": str(session["_id"]),
                        "timestamp": msg["response"]["timestamp"],
                    })

        return favorites

    async def _mark_response_favorited(
        self, response_id: str, is_favorited: bool
    ) -> bool:
        """
        Mark response as favorited in chat session.

        Args:
            response_id: Response message ID
            is_favorited: Favorited status

        Returns:
            True if updated
        """
        result = await self.sessions_collection.update_one(
            {"messages.message_id": response_id, "messages.type": "response"},
            {"$set": {"messages.$.response.is_favorited": is_favorited}},
        )

        return result.modified_count > 0

    async def is_favorited(self, user_id: ObjectId, response_id: str) -> bool:
        """
        Check if response is in user's favorites.

        Args:
            user_id: User's ObjectId
            response_id: Response message ID

        Returns:
            True if favorited
        """
        count = await self.users_collection.count_documents(
            {
                "_id": user_id,
                "favorite_response_ids": response_id,
            }
        )

        return count > 0
