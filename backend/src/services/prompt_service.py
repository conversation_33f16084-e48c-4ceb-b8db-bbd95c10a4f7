"""Prompt library service for suggestion retrieval."""

import logging
import random
from typing import Optional

from motor.motor_asyncio import AsyncIOMotorDatabase

from ..models.prompt_library import PromptLibrary, PromptLibraryInDB

logger = logging.getLogger(__name__)


class PromptService:
    """Service for prompt library management (FR-016, FR-017, FR-018)."""

    def __init__(self, db: AsyncIOMotorDatabase):
        """Initialize prompt service with database."""
        self.db = db
        self.prompts_collection = db.prompt_library

    async def get_random_suggestions(
        self,
        count: int = 3,
        category: Optional[str] = None,
        user_unlocked_themes: Optional[list[str]] = None,
    ) -> list[PromptLibraryInDB]:
        """
        Get random prompt suggestions (FR-017: 3-5 suggestions).

        Args:
            count: Number of suggestions (3-5)
            category: Optional category filter (FR-016)
            user_unlocked_themes: List of themes user has unlocked

        Returns:
            List of random prompt suggestions
        """
        # Validate count
        if count < 3 or count > 5:
            count = min(max(count, 3), 5)

        # Build query
        query = {}

        if category:
            query["category"] = category

        # Filter by unlocked themes (FR-018)
        if user_unlocked_themes is not None:
            # Include non-themed prompts and unlocked themed prompts
            query["$or"] = [
                {"is_themed": False},
                {"theme_id": {"$in": user_unlocked_themes}},
            ]

        # Get total count
        total = await self.prompts_collection.count_documents(query)

        if total == 0:
            logger.warning("No prompts found matching criteria")
            return []

        # Random sampling using aggregation
        pipeline = [
            {"$match": query},
            {"$sample": {"size": min(count, total)}},
        ]

        suggestions = []
        async for doc in self.prompts_collection.aggregate(pipeline):
            suggestions.append(PromptLibraryInDB(**doc))

        logger.info(
            f"Retrieved {len(suggestions)} random suggestions"
            + (f" for category {category}" if category else "")
        )

        return suggestions

    async def get_prompt_by_id(self, prompt_id: str) -> Optional[PromptLibraryInDB]:
        """
        Get prompt by ID.

        Args:
            prompt_id: Prompt ObjectId as string

        Returns:
            Prompt if found
        """
        from bson import ObjectId

        prompt_doc = await self.prompts_collection.find_one(
            {"_id": ObjectId(prompt_id)}
        )

        if not prompt_doc:
            return None

        return PromptLibraryInDB(**prompt_doc)

    async def check_theme_unlocked(
        self, theme_id: str, user_unlocked_themes: list[str]
    ) -> bool:
        """
        Check if user has unlocked a theme (FR-018, FR-019).

        Args:
            theme_id: Theme ID to check
            user_unlocked_themes: List of themes user has unlocked

        Returns:
            True if unlocked or not themed
        """
        if not theme_id:
            return True  # Non-themed prompts always unlocked

        return theme_id in user_unlocked_themes

    async def get_prompts_by_category(
        self,
        category: str,
        include_themed: bool = False,
        user_unlocked_themes: Optional[list[str]] = None,
    ) -> list[PromptLibraryInDB]:
        """
        Get all prompts in a category.

        Args:
            category: Category name
            include_themed: Include themed prompts
            user_unlocked_themes: User's unlocked themes (if including themed)

        Returns:
            List of prompts
        """
        query = {"category": category}

        if not include_themed:
            query["is_themed"] = False
        elif user_unlocked_themes is not None:
            query["$or"] = [
                {"is_themed": False},
                {"theme_id": {"$in": user_unlocked_themes}},
            ]

        prompts = []
        async for doc in self.prompts_collection.find(query):
            prompts.append(PromptLibraryInDB(**doc))

        return prompts

    async def get_themed_prompts(
        self, theme_id: str
    ) -> list[PromptLibraryInDB]:
        """
        Get all prompts for a specific theme.

        Args:
            theme_id: Theme ID

        Returns:
            List of themed prompts
        """
        prompts = []
        async for doc in self.prompts_collection.find({"theme_id": theme_id}):
            prompts.append(PromptLibraryInDB(**doc))

        return prompts

    async def get_all_categories(self) -> list[str]:
        """
        Get list of all categories.

        Returns:
            List of category names
        """
        categories = await self.prompts_collection.distinct("category")
        return sorted(categories)

    async def get_all_themes(self) -> list[str]:
        """
        Get list of all themed collections.

        Returns:
            List of theme IDs
        """
        themes = await self.prompts_collection.distinct("theme_id")
        return [t for t in themes if t]  # Filter out None values
