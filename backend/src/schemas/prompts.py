"""Prompt library request/response schemas."""

from typing import Optional

from pydantic import BaseModel, Field


class PromptSuggestionResponse(BaseModel):
    """Response schema for prompt suggestion."""

    suggestion_id: str
    prompt_text: str
    category: str
    theme_id: Optional[str] = None
    is_themed: bool = False
    difficulty: Optional[str] = "medium"


class PromptSuggestionsListResponse(BaseModel):
    """Response schema for list of prompt suggestions."""

    suggestions: list[PromptSuggestionResponse] = Field(min_length=3, max_length=5)


class PromptSuggestionsQuery(BaseModel):
    """Query parameters for prompt suggestions."""

    category: Optional[str] = Field(
        None,
        pattern="^(history|science|pop culture|impossible questions|food|movies)$",
    )
    count: int = Field(default=3, ge=3, le=5)
