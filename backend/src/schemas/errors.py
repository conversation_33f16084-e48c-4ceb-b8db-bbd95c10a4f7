"""Error response schemas with friendly messages (FR-039)."""

from typing import Any, Optional

from pydantic import BaseModel


class HTTPError(BaseModel):
    """Standard HTTP error response."""

    error: str
    message: str
    details: Optional[dict[str, Any]] = None


class ValidationError(HTTPError):
    """Validation error with friendly message."""

    error: str = "validation_error"
    message: str = "Ask me anything! I promise to get it hilariously wrong."


class ModerationError(HTTPError):
    """Content moderation error (FR-037, FR-038)."""

    error: str = "content_moderated"
    message: str = "Let's keep it fun and friendly! Try a different question."


class RoomNotFoundError(HTTPError):
    """Room not found error."""

    error: str = "room_not_found"
    message: str = "Hmm, that room doesn't exist. Double-check the code or create a new party!"


class RoomFullError(HTTPError):
    """Room full error (FR-043)."""

    error: str = "room_full"
    message: str = "Oops, this party's at max capacity (4 friends). Try creating your own room!"


class UnauthorizedError(HTTPError):
    """Unauthorized error."""

    error: str = "unauthorized"
    message: str = "You need to log in to access this feature."


class ForbiddenError(HTTPError):
    """Forbidden error."""

    error: str = "forbidden"
    message: str = "You don't have permission to access this resource."


class NotFoundError(HTTPError):
    """Generic not found error."""

    error: str = "not_found"
    message: str = "The resource you're looking for doesn't exist."


class InternalServerError(HTTPError):
    """Internal server error."""

    error: str = "internal_server_error"
    message: str = "Oops, something went wrong on our end. Please try again!"
