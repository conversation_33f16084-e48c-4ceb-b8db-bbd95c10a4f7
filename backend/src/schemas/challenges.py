"""Daily challenge request/response schemas."""

from datetime import datetime

from pydantic import BaseModel, Field


class DailyChallengeResponse(BaseModel):
    """Response schema for daily challenge."""

    challenge_id: str = Field(pattern="^\\d{8}$")
    question_text: str
    theme_category: str
    date: datetime
    completion_count: int


class ChallengeCompleteRequest(BaseModel):
    """Request schema for completing a challenge."""

    pass  # Challenge ID is in path parameter


class ChallengeCompleteResponse(BaseModel):
    """Response schema after completing a challenge."""

    streak_count: int
    unlocked_themes: list[str]
