"""Game room request/response schemas."""

from datetime import datetime
from typing import Optional

from pydantic import BaseModel, Field


class GameRoomCreate(BaseModel):
    """Request schema for creating a game room."""

    pass  # No required fields, room is created with defaults


class ParticipantSchema(BaseModel):
    """Participant schema for game room."""

    user_id: str
    display_name: str
    joined_at: datetime
    is_connected: bool = True


class TopResponseSchema(BaseModel):
    """Top response schema for game room."""

    response_id: str
    response_text: str
    vote_count: int
    round_number: int


class RoundResponseSchema(BaseModel):
    """Response in a game round."""

    response_id: str
    user_id: str
    response_text: str
    vote_count: int
    votes: list[dict]


class RoundSchema(BaseModel):
    """Round schema for game room."""

    round_number: int
    prompt: str
    responses: list[RoundResponseSchema]
    created_at: datetime


class GameRoomResponse(BaseModel):
    """Response schema for game room."""

    id: str
    room_code: str = Field(pattern="^[A-Z0-9]{6}$")
    creator_user_id: str
    status: str = Field(pattern="^(waiting|active|completed)$")
    participants: list[ParticipantSchema] = Field(max_length=4)
    max_participants: int = 4
    rounds: list[RoundSchema] = []
    top_responses: list[dict] = Field(default_factory=list, max_length=3)
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None


class JoinRoomRequest(BaseModel):
    """Request schema for joining a room."""

    pass  # Room code is in path parameter


class VoteRequest(BaseModel):
    """Request schema for voting on a response."""

    response_id: str


class SubmitPromptRequest(BaseModel):
    """Request schema for submitting prompt in game room."""

    text: str = Field(min_length=1, max_length=500)
