"""Admin moderation request/response schemas."""

from datetime import datetime

from pydantic import BaseModel


class FlaggedContentSchema(BaseModel):
    """Schema for flagged content (FR-040)."""

    user_id: str
    prompt_text: str
    matched_keywords: list[str]
    timestamp: datetime
    session_id: str


class FlaggedContentListResponse(BaseModel):
    """Response schema for list of flagged content."""

    flagged_prompts: list[FlaggedContentSchema]
    total: int


class ModerationActionRequest(BaseModel):
    """Request schema for moderation action."""

    action: str  # "approve" | "dismiss" | "ban_user"
    reason: str | None = None
