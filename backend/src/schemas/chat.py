"""Chat request/response schemas."""

from datetime import datetime
from typing import Optional
from uuid import UUID

from pydantic import BaseModel, Field


class ChatSessionCreate(BaseModel):
    """Request schema for creating chat session."""

    humor_style: Optional[str] = Field(
        None, pattern="^(absurd|sarcastic|whimsical|childlike|random)$"
    )


class PromptRequest(BaseModel):
    """Request schema for submitting a prompt."""

    session_id: str
    text: str = Field(min_length=1, max_length=500)
    humor_style: Optional[str] = Field(
        None, pattern="^(absurd|sarcastic|whimsical|childlike|random)$"
    )
    refinement: Optional[str] = Field(
        None, description='FR-015 - "make it sillier", "keep it short"'
    )


class ResponseSchema(BaseModel):
    """Response schema for AI-generated response."""

    text: str
    humor_style: str
    inference_time_ms: int
    model: str
    is_favorited: bool = False
    share_count: int = 0
    vote_count: int = 0


class PromptSchema(BaseModel):
    """Prompt schema in chat messages."""

    text: str
    moderation_status: str = "approved"


class ChatMessageSchema(BaseModel):
    """Chat message schema."""

    message_id: str
    type: str
    prompt: Optional[PromptSchema] = None
    response: Optional[ResponseSchema] = None
    timestamp: datetime


class ChatSessionResponse(BaseModel):
    """Response schema for chat session."""

    id: str
    user_id: str
    mode: str
    humor_style: str
    messages: list[ChatMessageSchema]
    session_start: datetime
    is_active: bool


class ChatSessionListResponse(BaseModel):
    """Response schema for list of chat sessions."""

    sessions: list[ChatSessionResponse]
    total: int


class RefinementRequest(BaseModel):
    """Request schema for response refinement (FR-015)."""

    session_id: str
    message_id: str
    instruction: str = Field(
        description='Refinement instruction like "make it sillier" or "keep it short"'
    )
