"""Authentication request/response schemas."""

from typing import Optional

from pydantic import BaseModel, EmailStr, Field


class RegisterRequest(BaseModel):
    """Request schema for user registration."""

    email: EmailStr
    password: str = Field(min_length=8)
    display_name: str = Field(min_length=2, max_length=50)
    consent_given: bool = Field(description="GDPR/LGPD consent (FR-041)")


class LoginRequest(BaseModel):
    """Request schema for user login."""

    email: EmailStr
    password: str


class TokenResponse(BaseModel):
    """Response schema for authentication tokens."""

    access_token: str
    refresh_token: str
    token_type: str = "Bearer"
    expires_in: int = 900  # 15 minutes in seconds
    user: "UserProfile"


class RefreshTokenRequest(BaseModel):
    """Request schema for token refresh."""

    refresh_token: str


class PasswordResetRequest(BaseModel):
    """Request schema for password reset."""

    email: EmailStr


class UserPreferencesSchema(BaseModel):
    """User preferences schema."""

    humor_style: str = Field(
        default="random",
        pattern="^(absurd|sarcastic|whimsical|childlike|random)$",
    )
    default_mode: str = Field(default="solo", pattern="^(solo|party|challenge)$")


class UserStatsSchema(BaseModel):
    """User statistics schema."""

    total_prompts: int = 0
    total_responses: int = 0
    total_votes_cast: int = 0
    total_shares: int = 0


class UserProfile(BaseModel):
    """User profile response schema."""

    id: str
    email: EmailStr
    display_name: str
    avatar_url: Optional[str] = None
    preferences: UserPreferencesSchema
    streak_count: int = 0
    unlocked_themes: list[str] = []
    stats: UserStatsSchema


class UpdateProfileRequest(BaseModel):
    """Request schema for profile update."""

    display_name: Optional[str] = Field(None, min_length=2, max_length=50)
    avatar_url: Optional[str] = None
    preferences: Optional[UserPreferencesSchema] = None


# Forward reference resolution
TokenResponse.model_rebuild()
