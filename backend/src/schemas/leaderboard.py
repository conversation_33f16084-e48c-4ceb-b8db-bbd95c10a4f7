"""Leaderboard request/response schemas."""

from datetime import datetime

from pydantic import BaseModel, Field


class LeaderboardEntrySchema(BaseModel):
    """Schema for single leaderboard entry."""

    response_id: str
    response_text: str
    prompt_text: str
    user_display_name: str
    vote_count: int
    share_count: int
    timestamp: datetime


class LeaderboardResponse(BaseModel):
    """Response schema for leaderboard."""

    entries: list[LeaderboardEntrySchema] = Field(max_length=100)
    generated_at: datetime
