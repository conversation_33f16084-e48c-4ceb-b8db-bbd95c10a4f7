"""Authentication routes (FR-001 to FR-006)."""

import logging

from fastapi import APIRouter, HTTPException, Request, status

from ..core.dependencies import AuthServiceDep
from ..models.user import UserInDB
from ..schemas.auth import (
    LoginRequest,
    PasswordResetRequest,
    RefreshTokenRequest,
    RegisterRequest,
    TokenResponse,
    UserProfile,
    UserPreferencesSchema,
    UserStatsSchema,
)

logger = logging.getLogger(__name__)

router = APIRouter()


def build_user_profile(user: UserInDB) -> UserProfile:
    """
    Convert UserInDB to UserProfile schema.

    Args:
        user: User from database

    Returns:
        UserProfile for API response
    """
    return UserProfile(
        id=str(user.id),
        email=user.email,
        display_name=user.display_name,
        avatar_url=user.avatar_url,
        preferences=UserPreferencesSchema(
            humor_style=user.preferences.humor_style,
            default_mode=user.preferences.default_mode
        ),
        streak_count=user.streak_count,
        unlocked_themes=user.unlocked_themes,
        stats=UserStatsSchema(
            total_prompts=user.stats.total_prompts,
            total_responses=user.stats.total_responses,
            total_votes_cast=user.stats.total_votes_cast,
            total_shares=user.stats.total_shares
        ),
    )


@router.post("/register", response_model=TokenResponse, status_code=status.HTTP_201_CREATED)
async def register(
    request: Request,
    registration: RegisterRequest,
    auth_service: AuthServiceDep,
) -> TokenResponse:
    """
    Register new user with email/password (FR-001, FR-002, FR-005, FR-041).

    Args:
        request: FastAPI request (for IP tracking)
        registration: User registration data
        auth_service: Auth service dependency

    Returns:
        JWT tokens and user profile

    Raises:
        HTTPException: If email exists or validation fails
    """
    try:
        # Get client IP for consent tracking
        client_ip = request.client.host if request.client else "unknown"

        # Register user
        user = await auth_service.register_user(
            email=registration.email,
            password=registration.password,
            display_name=registration.display_name,
            consent_given=registration.consent_given,
            ip_address=client_ip,
        )

        # Create tokens
        access_token = auth_service.create_access_token(str(user.id))
        refresh_token = auth_service.create_refresh_token(str(user.id))

        # Build user profile
        profile = build_user_profile(user)

        logger.info(f"User registered: {user.email}")

        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            user=profile,
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.post("/login", response_model=TokenResponse)
async def login(
    credentials: LoginRequest,
    auth_service: AuthServiceDep,
) -> TokenResponse:
    """
    Login with email/password (FR-003).

    Args:
        credentials: Login credentials
        auth_service: Auth service dependency

    Returns:
        JWT tokens and user profile

    Raises:
        HTTPException: If credentials invalid
    """
    user = await auth_service.login_user(
        email=credentials.email,
        password=credentials.password,
    )

    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password",
        )

    # Create tokens
    access_token = auth_service.create_access_token(str(user.id))
    refresh_token = auth_service.create_refresh_token(str(user.id))

    # Build user profile
    profile = build_user_profile(user)

    logger.info(f"User logged in: {user.email}")

    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        user=profile,
    )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    refresh_request: RefreshTokenRequest,
    auth_service: AuthServiceDep,
) -> TokenResponse:
    """
    Refresh access token using refresh token (FR-006).

    Args:
        refresh_request: Refresh token
        auth_service: Auth service dependency

    Returns:
        New JWT tokens and user profile

    Raises:
        HTTPException: If refresh token invalid
    """
    new_access_token = await auth_service.refresh_access_token(
        refresh_request.refresh_token
    )

    if not new_access_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired refresh token",
        )

    # Get user for profile
    user_id = auth_service.verify_token(
        refresh_request.refresh_token, token_type="refresh"
    )
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token",
        )

    user = await auth_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
        )

    # Create new refresh token
    new_refresh_token = auth_service.create_refresh_token(str(user.id))

    profile = build_user_profile(user)

    return TokenResponse(
        access_token=new_access_token,
        refresh_token=new_refresh_token,
        user=profile,
    )


@router.post("/reset-password", status_code=status.HTTP_200_OK)
async def request_password_reset(
    reset_request: PasswordResetRequest,
    auth_service: AuthServiceDep,
) -> dict[str, str]:
    """
    Request password reset (FR-004).

    Args:
        reset_request: Email for password reset
        auth_service: Auth service dependency

    Returns:
        Success message
    """
    # Create reset token
    reset_token = await auth_service.create_password_reset_token(reset_request.email)

    # In production, send email with reset link
    # For now, just log the token (NEVER do this in production!)
    if reset_token:
        logger.info(f"Password reset token for {reset_request.email}: {reset_token}")

    # Always return success to prevent email enumeration
    return {
        "message": "If that email exists, we've sent a password reset link. "
        "Please check your inbox."
    }


@router.get("/oauth/{provider}")
async def oauth_login(provider: str) -> dict[str, str]:
    """
    Initiate OAuth login flow (FR-001).

    Args:
        provider: OAuth provider (google, facebook)

    Returns:
        OAuth redirect URL

    Note:
        This is a placeholder. In production, implement full OAuth flow
        using authlib or similar library.
    """
    if provider not in ["google", "facebook"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported OAuth provider: {provider}",
        )

    # Placeholder - implement full OAuth flow in production
    return {
        "message": f"OAuth {provider} login not yet implemented",
        "redirect_url": f"https://oauth.{provider}.com/authorize",
    }


@router.post("/oauth/{provider}/callback")
async def oauth_callback(provider: str) -> TokenResponse:
    """
    Handle OAuth callback (FR-001).

    Args:
        provider: OAuth provider

    Returns:
        JWT tokens and user profile

    Note:
        This is a placeholder. Implement full OAuth callback handling.
    """
    if provider not in ["google", "facebook"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Unsupported OAuth provider: {provider}",
        )

    # Placeholder - implement OAuth callback in production
    raise HTTPException(
        status_code=status.HTTP_501_NOT_IMPLEMENTED,
        detail="OAuth callback not yet implemented",
    )


@router.post("/verify-email/{token}", status_code=status.HTTP_200_OK)
async def verify_email(
    token: str,
    auth_service: AuthServiceDep,
) -> dict[str, str]:
    """
    Verify email with verification token (FR-002).

    Args:
        token: Email verification token
        auth_service: Auth service dependency

    Returns:
        Success message

    Raises:
        HTTPException: If token invalid
    """
    verified = await auth_service.verify_email(token)

    if not verified:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired verification token",
        )

    return {"message": "Email verified successfully! You can now log in."}
