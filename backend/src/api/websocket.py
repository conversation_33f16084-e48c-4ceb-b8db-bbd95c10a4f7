"""WebSocket connection manager and event handlers for real-time features."""

import json
import logging
from typing import Any

from bson import ObjectId
from fastapi import WebSocket, WebSocketDisconnect

logger = logging.getLogger(__name__)


class ConnectionManager:
    """
    Manages WebSocket connections for real-time features.

    Supports:
    - Room-based messaging (party mode)
    - Broadcast to all connections
    - Individual connection management
    """

    def __init__(self) -> None:
        """Initialize connection manager."""
        # Active connections: {connection_id: websocket}
        self.active_connections: dict[str, WebSocket] = {}
        # Room subscriptions: {room_code: [connection_id, ...]}
        self.room_subscriptions: dict[str, list[str]] = {}
        # User mapping: {connection_id: user_id}
        self.user_mapping: dict[str, str] = {}

    async def connect(
        self, websocket: WebSocket, connection_id: str, user_id: str | None = None
    ) -> None:
        """
        Accept WebSocket connection.

        Args:
            websocket: WebSocket instance
            connection_id: Unique connection identifier
            user_id: Optional user ID for authentication
        """
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        if user_id:
            self.user_mapping[connection_id] = user_id
        logger.info(f"WebSocket connected: {connection_id} (user: {user_id})")

    def disconnect(self, connection_id: str) -> None:
        """
        Disconnect WebSocket.

        Args:
            connection_id: Connection identifier
        """
        # Remove from active connections
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]

        # Remove from user mapping
        if connection_id in self.user_mapping:
            del self.user_mapping[connection_id]

        # Remove from all room subscriptions
        for room_code in list(self.room_subscriptions.keys()):
            if connection_id in self.room_subscriptions[room_code]:
                self.room_subscriptions[room_code].remove(connection_id)
                # Clean up empty room subscriptions
                if not self.room_subscriptions[room_code]:
                    del self.room_subscriptions[room_code]

        logger.info(f"WebSocket disconnected: {connection_id}")

    async def send_personal_message(
        self, message: dict[str, Any], connection_id: str
    ) -> None:
        """
        Send message to specific connection.

        Args:
            message: Message data
            connection_id: Target connection
        """
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            await websocket.send_json(message)

    async def broadcast_to_room(self, message: dict[str, Any], room_code: str) -> None:
        """
        Broadcast message to all connections in room.

        Args:
            message: Message data
            room_code: Target room code
        """
        if room_code not in self.room_subscriptions:
            return

        for connection_id in self.room_subscriptions[room_code]:
            await self.send_personal_message(message, connection_id)

    async def subscribe_to_room(self, connection_id: str, room_code: str) -> None:
        """
        Subscribe connection to room.

        Args:
            connection_id: Connection identifier
            room_code: Room code to subscribe to
        """
        if room_code not in self.room_subscriptions:
            self.room_subscriptions[room_code] = []

        if connection_id not in self.room_subscriptions[room_code]:
            self.room_subscriptions[room_code].append(connection_id)
            logger.info(f"Connection {connection_id} subscribed to room {room_code}")

    async def unsubscribe_from_room(self, connection_id: str, room_code: str) -> None:
        """
        Unsubscribe connection from room.

        Args:
            connection_id: Connection identifier
            room_code: Room code to unsubscribe from
        """
        if (
            room_code in self.room_subscriptions
            and connection_id in self.room_subscriptions[room_code]
        ):
            self.room_subscriptions[room_code].remove(connection_id)
            logger.info(f"Connection {connection_id} unsubscribed from room {room_code}")

    def get_room_connections(self, room_code: str) -> list[str]:
        """
        Get all connection IDs in room.

        Args:
            room_code: Room code

        Returns:
            List of connection IDs
        """
        return self.room_subscriptions.get(room_code, [])


# Global connection manager instance
manager = ConnectionManager()


async def websocket_endpoint(websocket: WebSocket) -> None:
    """
    WebSocket endpoint handler.

    Handles events:
    - join_room: Join game room
    - leave_room: Leave game room
    - submit_prompt: Submit prompt in party mode
    - cast_vote: Cast vote for response

    Emits events:
    - user_joined: User joined room
    - user_left: User left room
    - response_generated: AI response ready
    - vote_cast: Vote cast for response
    - error: Error occurred
    """
    # Generate connection ID
    connection_id = f"ws_{id(websocket)}"

    # Accept connection
    await manager.connect(websocket, connection_id)

    try:
        # Send welcome message
        await manager.send_personal_message(
            {"event": "connected", "connection_id": connection_id}, connection_id
        )

        # Listen for messages
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)

            event_type = message.get("event")
            payload = message.get("payload", {})

            logger.info(f"WebSocket event received: {event_type} from {connection_id}")

            # Handle events
            if event_type == "join_room":
                await handle_join_room(connection_id, payload)

            elif event_type == "leave_room":
                await handle_leave_room(connection_id, payload)

            elif event_type == "submit_prompt":
                await handle_submit_prompt(connection_id, payload)

            elif event_type == "cast_vote":
                await handle_cast_vote(connection_id, payload)

            elif event_type == "ping":
                # Keep-alive
                await manager.send_personal_message(
                    {"event": "pong"}, connection_id
                )

            else:
                await manager.send_personal_message(
                    {
                        "event": "error",
                        "message": f"Unknown event type: {event_type}",
                    },
                    connection_id,
                )

    except WebSocketDisconnect:
        manager.disconnect(connection_id)
        logger.info(f"WebSocket disconnected: {connection_id}")

    except Exception as e:
        logger.error(f"WebSocket error: {e}", exc_info=True)
        manager.disconnect(connection_id)


async def handle_join_room(connection_id: str, payload: dict[str, Any]) -> None:
    """
    Handle join_room event.

    Args:
        connection_id: Connection identifier
        payload: Event payload with room_code, user_id, display_name
    """
    room_code = payload.get("room_code", "").upper()
    user_id = payload.get("user_id")
    display_name = payload.get("display_name", "Anonymous")

    if not room_code:
        await manager.send_personal_message(
            {"event": "error", "message": "room_code required"}, connection_id
        )
        return

    # Subscribe to room
    await manager.subscribe_to_room(connection_id, room_code)

    # Store user ID
    if user_id:
        manager.user_mapping[connection_id] = user_id

    # Broadcast user joined
    await manager.broadcast_to_room(
        {
            "event": "user_joined",
            "payload": {
                "room_code": room_code,
                "user_id": user_id,
                "display_name": display_name,
            },
        },
        room_code,
    )

    # Send confirmation
    await manager.send_personal_message(
        {
            "event": "joined_room",
            "payload": {"room_code": room_code},
        },
        connection_id,
    )


async def handle_leave_room(connection_id: str, payload: dict[str, Any]) -> None:
    """
    Handle leave_room event.

    Args:
        connection_id: Connection identifier
        payload: Event payload with room_code
    """
    room_code = payload.get("room_code", "").upper()

    if not room_code:
        return

    # Get user info before unsubscribing
    user_id = manager.user_mapping.get(connection_id)

    # Unsubscribe from room
    await manager.unsubscribe_from_room(connection_id, room_code)

    # Broadcast user left
    await manager.broadcast_to_room(
        {
            "event": "user_left",
            "payload": {
                "room_code": room_code,
                "user_id": user_id,
            },
        },
        room_code,
    )


async def handle_submit_prompt(connection_id: str, payload: dict[str, Any]) -> None:
    """
    Handle submit_prompt event.

    Args:
        connection_id: Connection identifier
        payload: Event payload with room_code, prompt_text
    """
    room_code = payload.get("room_code", "").upper()
    prompt_text = payload.get("prompt_text", "")

    if not room_code or not prompt_text:
        await manager.send_personal_message(
            {"event": "error", "message": "room_code and prompt_text required"},
            connection_id,
        )
        return

    # Broadcast prompt submitted (other users can see it)
    await manager.broadcast_to_room(
        {
            "event": "prompt_submitted",
            "payload": {
                "room_code": room_code,
                "prompt_text": prompt_text,
            },
        },
        room_code,
    )


async def handle_cast_vote(connection_id: str, payload: dict[str, Any]) -> None:
    """
    Handle cast_vote event.

    Args:
        connection_id: Connection identifier
        payload: Event payload with room_code, response_id
    """
    room_code = payload.get("room_code", "").upper()
    response_id = payload.get("response_id")
    user_id = manager.user_mapping.get(connection_id)

    if not room_code or not response_id:
        await manager.send_personal_message(
            {"event": "error", "message": "room_code and response_id required"},
            connection_id,
        )
        return

    # Broadcast vote cast
    await manager.broadcast_to_room(
        {
            "event": "vote_cast",
            "payload": {
                "room_code": room_code,
                "response_id": response_id,
                "user_id": user_id,
            },
        },
        room_code,
    )
