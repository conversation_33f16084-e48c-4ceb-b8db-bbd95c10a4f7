"""Game room routes for party mode (FR-021 to FR-026, FR-043)."""

import logging

from bson import ObjectId
from fastapi import APIRouter, HTTPException, status

from ..core.dependencies import CurrentUserDep, RoomServiceDep
from ..schemas.rooms import (
    GameRoomCreate,
    GameRoomResponse,
    JoinRoomRequest,
    VoteRequest,
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("", response_model=GameRoomResponse, status_code=status.HTTP_201_CREATED)
async def create_room(
    room_data: GameRoomCreate,
    current_user: CurrentUserDep,
    room_service: RoomServiceDep,
) -> GameRoomResponse:
    """
    Create new game room (FR-021, FR-022).

    Args:
        room_data: Room creation data
        current_user: Current authenticated user
        room_service: Room service dependency

    Returns:
        Created game room with unique code
    """
    room = await room_service.create_room(
        creator_user_id=current_user.id,
        creator_display_name=current_user.display_name,
    )

    logger.info(
        f"Room {room.room_code} created by user {current_user.email}"
    )

    return GameRoomResponse(
        id=str(room.id),
        room_code=room.room_code,
        creator_user_id=str(room.creator_user_id),
        status=room.status,
        max_participants=4,
        participants=[
            {
                "user_id": str(p.user_id),
                "display_name": p.display_name,
                "is_connected": p.is_connected,
                "joined_at": p.joined_at,
            }
            for p in room.participants
        ],
        rounds=[],
        top_responses=[],
        created_at=room.created_at,
        started_at=room.started_at,
        completed_at=room.completed_at,
    )


@router.get("/{room_code}", response_model=GameRoomResponse)
async def get_room(
    room_code: str,
    room_service: RoomServiceDep,
) -> GameRoomResponse:
    """
    Get room details by code (FR-024).

    Args:
        room_code: 6-character room code
        room_service: Room service dependency

    Returns:
        Game room details

    Raises:
        HTTPException: If room not found
    """
    room = await room_service.get_room_by_code(room_code.upper())

    if not room:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Room {room_code} not found. Double-check the code and try again!",
        )

    return GameRoomResponse(
        id=str(room.id),
        room_code=room.room_code,
        creator_user_id=str(room.creator_user_id),
        status=room.status,
        max_participants=4,
        participants=[
            {
                "user_id": str(p.user_id),
                "display_name": p.display_name,
                "is_connected": p.is_connected,
                "joined_at": p.joined_at,
            }
            for p in room.participants
        ],
        rounds=[
            {
                "round_number": r.round_number,
                "prompt": r.prompts[0].text if r.prompts else "",
                "responses": [
                    {
                        "response_id": resp.response_id,
                        "user_id": "unknown",
                        "response_text": resp.response_text,
                        "vote_count": resp.vote_count,
                        "votes": [{"user_id": str(v.user_id), "voted_at": v.timestamp.isoformat()} for v in resp.votes],
                    }
                    for resp in r.responses
                ],
                "created_at": r.round_start,
            }
            for r in room.rounds
        ],
        top_responses=room.top_responses,
        created_at=room.created_at,
        started_at=room.started_at,
        completed_at=room.completed_at,
    )


@router.post("/{room_code}/join", response_model=GameRoomResponse)
async def join_room(
    room_code: str,
    join_data: JoinRoomRequest,
    current_user: CurrentUserDep,
    room_service: RoomServiceDep,
) -> GameRoomResponse:
    """
    Join existing game room (FR-023, FR-043).

    Args:
        room_code: 6-character room code
        join_data: Join request data
        current_user: Current authenticated user
        room_service: Room service dependency

    Returns:
        Updated game room

    Raises:
        HTTPException: If room not found or full (max 4)
    """
    try:
        await room_service.add_participant(
            room_code=room_code.upper(),
            user_id=current_user.id,
            display_name=current_user.display_name,
        )

        # Get updated room
        room = await room_service.get_room_by_code(room_code.upper())
        if not room:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Room not found",
            )

        logger.info(
            f"User {current_user.email} joined room {room_code}"
        )

        return GameRoomResponse(
            id=str(room.id),
            room_code=room.room_code,
            creator_user_id=str(room.creator_user_id),
            status=room.status,
            max_participants=4,
            participants=[
                {
                    "user_id": str(p.user_id),
                    "display_name": p.display_name,
                    "is_connected": p.is_connected,
                    "joined_at": p.joined_at,
                }
                for p in room.participants
            ],
            rounds=[],
            top_responses=[],
            created_at=room.created_at,
            started_at=room.started_at,
            completed_at=room.completed_at,
        )

    except ValueError as e:
        error_msg = str(e)
        if "full" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="This room is full! Maximum 4 players allowed. Try creating a new room!",
            )
        elif "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Room {room_code} not found. Double-check the code and try again!",
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg,
            )


@router.post("/{room_code}/vote", status_code=status.HTTP_200_OK)
async def vote_for_response(
    room_code: str,
    vote_data: VoteRequest,
    current_user: CurrentUserDep,
    room_service: RoomServiceDep,
) -> dict[str, str]:
    """
    Vote for a response in the room (FR-025).

    Args:
        room_code: 6-character room code
        vote_data: Vote request data
        current_user: Current authenticated user
        room_service: Room service dependency

    Returns:
        Success message

    Raises:
        HTTPException: If room not found or vote fails
    """
    try:
        voted = await room_service.cast_vote(
            room_code=room_code.upper(),
            user_id=current_user.id,
            response_id=vote_data.response_id,
        )

        if not voted:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to cast vote. Response may not exist.",
            )

        # Update user stats
        from ..services.user_service import UserService

        user_service = UserService(room_service.db)
        await user_service.increment_stats(
            user_id=current_user.id,
            stat_name="total_votes_cast",
        )

        logger.info(
            f"User {current_user.email} voted for response {vote_data.response_id} "
            f"in room {room_code}"
        )

        return {"message": "Vote cast successfully!"}

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.get("/{room_code}/results", response_model=dict)
async def get_room_results(
    room_code: str,
    room_service: RoomServiceDep,
) -> dict:
    """
    Get top 3 responses for room (FR-026).

    Args:
        room_code: 6-character room code
        room_service: Room service dependency

    Returns:
        Top 3 responses sorted by votes

    Raises:
        HTTPException: If room not found
    """
    room = await room_service.get_room_by_code(room_code.upper())

    if not room:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Room {room_code} not found",
        )

    # Calculate top responses
    top_responses = await room_service.calculate_top_responses(
        room_code=room_code.upper(),
        limit=3,
    )

    return {
        "room_code": room.room_code,
        "status": room.status,
        "top_responses": top_responses,
    }


@router.post("/{room_code}/end", status_code=status.HTTP_200_OK)
async def end_room_session(
    room_code: str,
    current_user: CurrentUserDep,
    room_service: RoomServiceDep,
) -> dict[str, str]:
    """
    End game session (creator only).

    Args:
        room_code: 6-character room code
        current_user: Current authenticated user
        room_service: Room service dependency

    Returns:
        Success message with top results

    Raises:
        HTTPException: If not room creator or room not found
    """
    room = await room_service.get_room_by_code(room_code.upper())

    if not room:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Room {room_code} not found",
        )

    # Check if user is creator
    if room.creator_user_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only the room creator can end the session",
        )

    # End session
    await room_service.end_session(room_code=room_code.upper())

    logger.info(f"Room {room_code} ended by creator {current_user.email}")

    return {
        "message": "Game session ended! Check the results to see who won!",
    }
