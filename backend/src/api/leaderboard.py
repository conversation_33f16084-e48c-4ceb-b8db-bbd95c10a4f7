"""Leaderboard routes (FR-036)."""

import logging

from fastapi import APIRouter, HTTPException, Query, status

from ..core.dependencies import LeaderboardServiceDep
from ..schemas.leaderboard import LeaderboardEntrySchema, LeaderboardResponse

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("", response_model=LeaderboardResponse)
async def get_leaderboard(
    leaderboard_service: LeaderboardServiceDep,
    board_type: str = Query(
        "most_liked",
        description="Leaderboard type: most_liked or most_shared",
        pattern="^(most_liked|most_shared)$",
    ),
    limit: int = Query(100, ge=10, le=100, description="Number of entries (max 100)"),
) -> LeaderboardResponse:
    """
    Get global leaderboard (FR-036).

    Returns top 100 users sorted by vote count or share count.
    Cached for 5 minutes.

    Args:
        leaderboard_service: Leaderboard service dependency
        board_type: Type of leaderboard (most_liked or most_shared)
        limit: Number of entries (default 100)

    Returns:
        Leaderboard with top entries
    """
    try:
        # Get cached leaderboard
        leaderboard_cache = await leaderboard_service.get_cached_leaderboard(
            cache_type=board_type
        )

        if not leaderboard_cache:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Leaderboard temporarily unavailable. Try again in a moment!",
            )

        # Convert to response format
        entries = []
        for idx, entry in enumerate(leaderboard_cache.entries[:limit], start=1):
            entries.append(
                LeaderboardEntrySchema(
                    rank=idx,
                    user_id=str(entry.user_id),
                    display_name=entry.display_name,
                    vote_count=entry.vote_count,
                    share_count=entry.share_count,
                    total_prompts=entry.total_prompts,
                )
            )

        logger.info(f"Leaderboard requested: {board_type} (limit: {limit})")

        return LeaderboardResponse(
            board_type=board_type,
            entries=entries,
            total_entries=len(entries),
            last_updated=leaderboard_cache.last_updated,
        )

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.get("/user/{user_id}", response_model=dict)
async def get_user_rank(
    user_id: str,
    leaderboard_service: LeaderboardServiceDep,
    board_type: str = Query(
        "most_liked",
        description="Leaderboard type",
        pattern="^(most_liked|most_shared)$",
    ),
) -> dict:
    """
    Get user's rank in leaderboard.

    Args:
        user_id: User ID to look up
        leaderboard_service: Leaderboard service dependency
        board_type: Type of leaderboard

    Returns:
        User's rank and stats
    """
    try:
        # Get cached leaderboard
        leaderboard_cache = await leaderboard_service.get_cached_leaderboard(
            cache_type=board_type
        )

        if not leaderboard_cache:
            raise HTTPException(
                status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
                detail="Leaderboard temporarily unavailable",
            )

        # Find user in leaderboard
        user_rank = None
        user_entry = None

        for idx, entry in enumerate(leaderboard_cache.entries, start=1):
            if str(entry.user_id) == user_id:
                user_rank = idx
                user_entry = entry
                break

        if not user_rank:
            return {
                "message": "User not in top 100",
                "rank": None,
                "in_leaderboard": False,
            }

        return {
            "rank": user_rank,
            "display_name": user_entry.display_name,
            "vote_count": user_entry.vote_count,
            "share_count": user_entry.share_count,
            "total_prompts": user_entry.total_prompts,
            "in_leaderboard": True,
        }

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
