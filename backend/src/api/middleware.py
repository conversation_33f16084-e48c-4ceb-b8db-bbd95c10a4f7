"""Middleware for authentication, rate limiting, and logging."""

import time
from collections import defaultdict
from functools import wraps
from typing import Any, Callable

from fastapi import Depends, HTTPException, Request, Response, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from ..core.dependencies import get_current_user
from ..core.logging import get_logger
from ..models.user import UserInDB

logger = get_logger(__name__)


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Rate limiting middleware (FR-010: 10 prompts/min per research.md).

    Implements a simple in-memory sliding window rate limiter.
    For production, use Redis-based rate limiting.
    """

    def __init__(self, app: Callable, max_requests: int = 10, window_seconds: int = 60):
        """
        Initialize rate limiter.

        Args:
            app: FastAPI app
            max_requests: Maximum requests per window
            window_seconds: Time window in seconds
        """
        super().__init__(app)
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        # In-memory storage: {ip: [(timestamp, path), ...]}
        self.requests: dict[str, list[tuple[float, str]]] = defaultdict(list)

    def _cleanup_old_requests(self, ip: str, current_time: float) -> None:
        """Remove requests outside the time window."""
        cutoff_time = current_time - self.window_seconds
        self.requests[ip] = [
            (ts, path) for ts, path in self.requests[ip] if ts > cutoff_time
        ]

    def _is_rate_limited(self, ip: str, path: str) -> bool:
        """
        Check if request should be rate limited.

        Args:
            ip: Client IP address
            path: Request path

        Returns:
            True if rate limited, False otherwise
        """
        current_time = time.time()

        # Cleanup old requests
        self._cleanup_old_requests(ip, current_time)

        # Only rate limit prompt endpoints
        rate_limited_paths = ["/api/chat/prompt", "/api/rooms/"]
        if not any(p in path or path.startswith(p) for p in rate_limited_paths):
            return False

        # Check request count
        request_count = len(self.requests[ip])
        if request_count >= self.max_requests:
            return True

        # Add current request
        self.requests[ip].append((current_time, path))
        return False

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Process request with rate limiting.

        Args:
            request: FastAPI request
            call_next: Next middleware/route handler

        Returns:
            Response or rate limit error
        """
        # Get client IP
        client_ip = request.client.host if request.client else "unknown"

        # Check rate limit
        if self._is_rate_limited(client_ip, request.url.path):
            logger.warning(
                "rate_limit_exceeded",
                client_ip=client_ip,
                path=request.url.path,
                limit=self.max_requests,
                window_seconds=self.window_seconds,
            )
            return JSONResponse(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                content={
                    "error": "rate_limit_exceeded",
                    "message": "Whoa there, speed racer! You're sending requests too fast. "
                    "Take a short break and try again in a minute.",
                    "details": {
                        "limit": f"{self.max_requests} requests per {self.window_seconds} seconds",
                        "help": f"Wait {self.window_seconds} seconds before trying again.",
                    },
                },
            )

        # Process request
        response = await call_next(request)
        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for logging requests and performance metrics."""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        Log request and response with timing.

        Args:
            request: FastAPI request
            call_next: Next middleware/route handler

        Returns:
            Response
        """
        start_time = time.time()
        client_ip = request.client.host if request.client else "unknown"

        # Log request
        logger.info(
            "http_request",
            method=request.method,
            path=request.url.path,
            client_ip=client_ip,
            query_params=dict(request.query_params) if request.query_params else None,
        )

        # Process request
        response = await call_next(request)

        # Calculate duration
        duration_ms = (time.time() - start_time) * 1000

        # Log response
        logger.info(
            "http_response",
            method=request.method,
            path=request.url.path,
            status_code=response.status_code,
            duration_ms=round(duration_ms, 2),
            client_ip=client_ip,
        )

        # Add performance header
        response.headers["X-Response-Time"] = f"{duration_ms:.2f}ms"

        return response


def admin_required(func: Callable[..., Any]) -> Callable[..., Any]:
    """
    Decorator for routes that require admin role.

    Args:
        func: FastAPI route handler function

    Returns:
        Wrapped function that checks for admin role
    """

    @wraps(func)
    async def wrapper(*args: Any, current_user: UserInDB = Depends(get_current_user), **kwargs: Any) -> Any:
        if current_user.role != "admin":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Admin access required",
            )
        return await func(*args, current_user=current_user, **kwargs)

    return wrapper
