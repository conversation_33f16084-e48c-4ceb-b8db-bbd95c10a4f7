"""Prompt library routes (FR-016, FR-017, FR-018)."""

import logging

from fastapi import APIRouter, HTTPException, Query, status

from ..core.dependencies import CurrentUserDep, PromptServiceDep
from ..schemas.prompts import PromptSuggestionResponse

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/suggestions", response_model=list[PromptSuggestionResponse])
async def get_suggestions(
    current_user: CurrentUserDep,
    prompt_service: PromptServiceDep,
    category: str | None = Query(None, description="Filter by category"),
    count: int = Query(5, ge=3, le=10, description="Number of suggestions (3-10)"),
) -> list[PromptSuggestionResponse]:
    """
    Get prompt suggestions (FR-016, FR-017, FR-018).

    Returns 3-5 random suggestions, optionally filtered by category.
    Only shows themed prompts if user has unlocked them.

    Args:
        current_user: Current authenticated user
        prompt_service: Prompt service dependency
        category: Optional category filter
        count: Number of suggestions (default 5, FR-017)

    Returns:
        List of prompt suggestions
    """
    try:
        suggestions = await prompt_service.get_random_suggestions(
            category=category,
            count=count,
        )

        return [
            PromptSuggestionResponse(
                suggestion_id=str(suggestion.id),
                prompt_text=suggestion.question_text,
                category=suggestion.category,
                difficulty=suggestion.difficulty or "medium",
                is_themed=suggestion.is_themed,
                theme_id=suggestion.theme_id,
            )
            for suggestion in suggestions
        ]

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.get("/categories", response_model=list[str])
async def get_categories(
    prompt_service: PromptServiceDep,
) -> list[str]:
    """
    Get available prompt categories.

    Args:
        prompt_service: Prompt service dependency

    Returns:
        List of category names
    """
    categories = await prompt_service.get_all_categories()
    return categories


@router.get("/themes", response_model=list[dict])
async def get_available_themes(
    current_user: CurrentUserDep,
    prompt_service: PromptServiceDep,
) -> list[dict]:
    """
    Get themes and their unlock status for current user (FR-018, FR-019).

    Args:
        current_user: Current authenticated user
        prompt_service: Prompt service dependency

    Returns:
        List of themes with unlock status
    """
    themes = await prompt_service.get_themes_for_user(current_user.id)
    return themes
