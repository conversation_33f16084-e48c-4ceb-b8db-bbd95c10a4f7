"""Error handlers for friendly user-facing error messages."""

import logging
from typing import Any

from fastapi import Request, status
from fastapi.responses import JSONResponse

logger = logging.getLogger(__name__)


class ModerationException(Exception):
    """Exception for content moderation blocks."""

    def __init__(self, message: str, matched_keywords: list[str] | None = None):
        """Initialize moderation exception."""
        self.message = message
        self.matched_keywords = matched_keywords or []
        super().__init__(self.message)


async def validation_exception_handler(
    request: Request, exc: ValueError
) -> JSONResponse:
    """
    Handle validation errors with friendly messages (FR-039).

    Args:
        request: FastAPI request
        exc: ValueError exception

    Returns:
        JSON response with friendly error message
    """
    logger.warning(f"Validation error on {request.url.path}: {str(exc)}")

    # Map common validation errors to friendly messages
    error_message = str(exc)
    friendly_message = error_message

    if "email" in error_message.lower():
        friendly_message = "Please enter a valid email address."
    elif "password" in error_message.lower() and "8" in error_message:
        friendly_message = "Password must be at least 8 characters long."
    elif "display_name" in error_message.lower():
        friendly_message = "Display name must be 2-50 characters with only letters, numbers, spaces, and underscores."
    elif "consent" in error_message.lower():
        friendly_message = "Please accept the privacy policy to continue."
    elif "room full" in error_message.lower():
        friendly_message = "This room is full! Maximum 4 players allowed."
    elif "not found" in error_message.lower():
        friendly_message = error_message  # Already friendly

    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={
            "error": "validation_error",
            "message": friendly_message,
            "details": {"original": error_message},
        },
    )


async def moderation_exception_handler(
    request: Request, exc: ModerationException
) -> JSONResponse:
    """
    Handle moderation blocks with 451 status (FR-037, FR-038, FR-039).

    Args:
        request: FastAPI request
        exc: ModerationException

    Returns:
        JSON response with 451 status and friendly message
    """
    logger.info(
        f"Content moderated on {request.url.path}: {exc.message} "
        f"(keywords: {exc.matched_keywords})"
    )

    return JSONResponse(
        status_code=status.HTTP_451_UNAVAILABLE_FOR_LEGAL_REASONS,
        content={
            "error": "content_blocked",
            "message": "Your message contains content that doesn't meet our community guidelines. "
            "Please try rephrasing in a more friendly way!",
            "details": {
                "help": "Keep it fun and family-friendly. Avoid offensive or inappropriate language.",
            },
        },
    )


async def http_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle generic exceptions with friendly messages.

    Args:
        request: FastAPI request
        exc: Generic exception

    Returns:
        JSON response with friendly error message
    """
    logger.error(f"Unhandled exception on {request.url.path}: {str(exc)}", exc_info=exc)

    # Don't expose internal errors to users
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "error": "internal_error",
            "message": "Oops! Something went wrong on our end. Please try again in a moment.",
            "details": {
                "help": "If this keeps happening, please contact support.",
            },
        },
    )


async def not_found_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle 404 not found errors.

    Args:
        request: FastAPI request
        exc: Exception

    Returns:
        JSON response with friendly 404 message
    """
    return JSONResponse(
        status_code=status.HTTP_404_NOT_FOUND,
        content={
            "error": "not_found",
            "message": "We couldn't find what you're looking for!",
            "details": {
                "path": str(request.url.path),
                "help": "Check the URL and try again.",
            },
        },
    )


async def rate_limit_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Handle rate limit errors.

    Args:
        request: FastAPI request
        exc: Exception

    Returns:
        JSON response with friendly rate limit message
    """
    return JSONResponse(
        status_code=status.HTTP_429_TOO_MANY_REQUESTS,
        content={
            "error": "rate_limit_exceeded",
            "message": "Whoa there, speed racer! You're sending requests too fast. "
            "Take a short break and try again in a minute.",
            "details": {
                "limit": "10 requests per minute",
                "help": "Wait 60 seconds before trying again.",
            },
        },
    )


async def empty_prompt_handler(request: Request) -> JSONResponse:
    """
    Handle empty prompt submissions (edge case).

    Args:
        request: FastAPI request

    Returns:
        JSON response with friendly empty prompt message
    """
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={
            "error": "empty_prompt",
            "message": "Don't be shy! Type something and I'll give you a hilariously wrong answer!",
            "details": {
                "help": "Enter a question or prompt to get started.",
            },
        },
    )
