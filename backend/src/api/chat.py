"""Chat routes for solo mode (FR-008 to FR-015, FR-020, FR-030 to FR-032, FR-035)."""

import logging
import time

from bson import ObjectId
from fastapi import APIRouter, HTTPException, Query, status

from ..api.error_handlers import ModerationException, empty_prompt_handler
from ..core.dependencies import (
    AIServiceDep,
    ChatServiceDep,
    CurrentUserDep,
    FavoritesServiceDep,
    ModerationServiceDep,
    ShareServiceDep,
)
from ..schemas.chat import (
    ChatSessionCreate,
    ChatSessionListResponse,
    ChatSessionResponse,
    PromptRequest,
    RefinementRequest,
    ResponseSchema,
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/sessions", response_model=ChatSessionResponse, status_code=status.HTTP_201_CREATED)
async def create_session(
    session_data: ChatSessionCreate,
    current_user: CurrentUserDep,
    chat_service: ChatServiceDep,
) -> ChatSessionResponse:
    """
    Create new chat session (FR-020).

    Args:
        session_data: Session creation data
        current_user: Current authenticated user
        chat_service: Chat service dependency

    Returns:
        Created chat session
    """
    # Use user's default humor style if not provided
    humor_style = session_data.humor_style or current_user.preferences.humor_style

    session = await chat_service.create_session(
        user_id=current_user.id,
        mode="solo",
        humor_style=humor_style,
    )

    logger.info(f"Created chat session {session.id} for user {current_user.email}")

    return ChatSessionResponse(
        id=str(session.id),
        user_id=str(session.user_id),
        mode=session.mode,
        humor_style=session.humor_style,
        messages=[],
        session_start=session.session_start,
        is_active=session.is_active,
    )


@router.get("/sessions", response_model=ChatSessionListResponse)
async def list_sessions(
    current_user: CurrentUserDep,
    chat_service: ChatServiceDep,
    limit: int = Query(10, ge=1, le=50),
    offset: int = Query(0, ge=0),
) -> ChatSessionListResponse:
    """
    Get user's chat sessions (FR-014 chat history).

    Args:
        current_user: Current authenticated user
        chat_service: Chat service dependency
        limit: Maximum number of sessions
        offset: Pagination offset

    Returns:
        List of chat sessions
    """
    sessions, total = await chat_service.get_user_sessions(
        user_id=current_user.id,
        limit=limit,
        offset=offset,
    )

    session_responses = []
    for session in sessions:
        session_responses.append(
            ChatSessionResponse(
                id=str(session.id),
                user_id=str(session.user_id),
                mode=session.mode,
                humor_style=session.humor_style,
                messages=[],  # Don't include full messages in list
                session_start=session.session_start,
                is_active=session.is_active,
            )
        )

    return ChatSessionListResponse(
        sessions=session_responses,
        total=total,
    )


@router.get("/sessions/{session_id}", response_model=ChatSessionResponse)
async def get_session(
    session_id: str,
    current_user: CurrentUserDep,
    chat_service: ChatServiceDep,
) -> ChatSessionResponse:
    """
    Get chat session with full history (FR-013, FR-014).

    Args:
        session_id: Chat session ID
        current_user: Current authenticated user
        chat_service: Chat service dependency

    Returns:
        Chat session with messages

    Raises:
        HTTPException: If session not found or unauthorized
    """
    try:
        session_obj_id = ObjectId(session_id)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid session ID format",
        )

    session = await chat_service.get_session_by_id(
        session_id=session_obj_id,
        user_id=current_user.id,
    )

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found",
        )

    from ..schemas.chat import ChatMessageSchema, PromptSchema

    messages = []
    for msg in session.messages:
        prompt_schema = None
        response_schema = None

        if msg.prompt:
            prompt_schema = PromptSchema(
                text=msg.prompt.text,
                moderation_status=msg.prompt.moderation_status,
            )

        if msg.response:
            response_schema = ResponseSchema(
                text=msg.response.text,
                humor_style=msg.response.humor_style,
                inference_time_ms=msg.response.inference_time_ms,
                model=msg.response.model,
                is_favorited=msg.response.is_favorited,
                share_count=msg.response.share_count,
                vote_count=msg.response.vote_count,
            )

        messages.append(
            ChatMessageSchema(
                message_id=msg.message_id,
                type=msg.type,
                prompt=prompt_schema,
                response=response_schema,
                timestamp=msg.prompt.timestamp if msg.prompt else msg.response.timestamp,
            )
        )

    return ChatSessionResponse(
        id=str(session.id),
        user_id=str(session.user_id),
        mode=session.mode,
        humor_style=session.humor_style,
        messages=messages,
        session_start=session.session_start,
        is_active=session.is_active,
    )


@router.post("/prompt", response_model=ResponseSchema)
async def submit_prompt(
    prompt_data: PromptRequest,
    current_user: CurrentUserDep,
    chat_service: ChatServiceDep,
    ai_service: AIServiceDep,
    moderation_service: ModerationServiceDep,
) -> ResponseSchema:
    """
    Submit prompt and get AI response (FR-008 to FR-012, FR-015).

    Args:
        prompt_data: User prompt
        current_user: Current authenticated user
        chat_service: Chat service dependency
        ai_service: AI service dependency
        moderation_service: Moderation service dependency

    Returns:
        AI-generated response

    Raises:
        HTTPException: If session not found or moderation blocks
        ModerationException: If content violates guidelines
    """
    # Check for empty prompt
    if not prompt_data.text.strip():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Don't be shy! Type something and I'll give you a hilariously wrong answer!",
        )

    # Get session
    try:
        session_obj_id = ObjectId(prompt_data.session_id)
    except Exception:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid session ID format",
        )

    session = await chat_service.get_session_by_id(
        session_id=session_obj_id,
        user_id=current_user.id,
    )

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found",
        )

    # Check content moderation (FR-037, FR-038)
    moderation_status, matched_keywords = await moderation_service.check_prompt_safety(
        prompt_data.text,
        user_id=current_user.id,
        session_id=str(session_obj_id)
    )

    if moderation_status != "approved":
        # Log for admin review (FR-040)
        await moderation_service.flag_for_review(
            user_id=current_user.id,
            content=prompt_data.text,
            matched_keywords=matched_keywords,
            content_type="prompt",
        )

        # Add blocked prompt to session
        await chat_service.add_prompt(
            session_id=session_obj_id,
            prompt_text=prompt_data.text,
            moderation_status="blocked",
        )

        raise ModerationException(
            "Content blocked by moderation",
            matched_keywords=matched_keywords,
        )

    # Add approved prompt to session
    await chat_service.add_prompt(
        session_id=session_obj_id,
        prompt_text=prompt_data.text,
        moderation_status="approved",
    )

    # Determine humor style
    humor_style = prompt_data.humor_style or session.humor_style

    # Generate AI response (FR-010: <2s target)
    start_time = time.time()
    try:
        ai_response = await ai_service.generate_wrong_answer(
            prompt=prompt_data.text,
            humor_style=humor_style,
            timeout=15.0,  # Increased for CPU-based inference
        )
    except TimeoutError:
        raise HTTPException(
            status_code=status.HTTP_504_GATEWAY_TIMEOUT,
            detail="AI is taking too long to think of something silly! Try again in a moment.",
        )
    except Exception as e:
        logger.error(f"AI generation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Our AI got confused! Please try again.",
        )

    inference_time_ms = int((time.time() - start_time) * 1000)

    # Validate AI output (FR-038)
    is_response_safe = await moderation_service.validate_response_safety(
        ai_response.answer
    )

    if not is_response_safe:
        logger.warning(f"AI generated unsafe content: {ai_response.answer[:50]}...")
        # Regenerate or use fallback
        ai_response.answer = "Oops! My humor circuits got crossed. Ask me something else!"

    # Add response to session
    await chat_service.add_response(
        session_id=session_obj_id,
        response_text=ai_response.answer,
        inference_time_ms=inference_time_ms,
        model="phi-4-mini-instruct",
        humor_style=ai_response.style,
    )

    # Update user stats
    from ..services.user_service import UserService

    user_service = UserService(chat_service.db)
    await user_service.increment_stats(
        user_id=current_user.id,
        stat_name="total_prompts",
    )

    logger.info(
        f"Prompt processed for user {current_user.email} "
        f"in {inference_time_ms}ms (style: {humor_style})"
    )

    return ResponseSchema(
        text=ai_response.answer,
        humor_style=ai_response.style,
        inference_time_ms=inference_time_ms,
        model="phi-4-mini-instruct",
        is_favorited=False,
        share_count=0,
        vote_count=0,
    )


@router.post("/responses/{response_id}/favorite", status_code=status.HTTP_200_OK)
async def favorite_response(
    response_id: str,
    current_user: CurrentUserDep,
    favorites_service: FavoritesServiceDep,
) -> dict[str, str]:
    """
    Add response to favorites (FR-035).

    Args:
        response_id: Response message ID
        current_user: Current authenticated user
        favorites_service: Favorites service dependency

    Returns:
        Success message
    """
    try:
        await favorites_service.add_to_favorites(
            user_id=current_user.id,
            response_id=response_id,
        )

        logger.info(f"User {current_user.email} favorited response {response_id}")

        return {"message": "Response added to favorites!"}

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.delete("/responses/{response_id}/favorite", status_code=status.HTTP_200_OK)
async def unfavorite_response(
    response_id: str,
    current_user: CurrentUserDep,
    favorites_service: FavoritesServiceDep,
) -> dict[str, str]:
    """
    Remove response from favorites (FR-035).

    Args:
        response_id: Response message ID
        current_user: Current authenticated user
        favorites_service: Favorites service dependency

    Returns:
        Success message
    """
    try:
        await favorites_service.remove_from_favorites(
            user_id=current_user.id,
            response_id=response_id,
        )

        logger.info(f"User {current_user.email} unfavorited response {response_id}")

        return {"message": "Response removed from favorites!"}

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.post("/responses/{response_id}/share", status_code=status.HTTP_200_OK)
async def share_response(
    response_id: str,
    current_user: CurrentUserDep,
    share_service: ShareServiceDep,
) -> dict[str, str]:
    """
    Generate shareable link for response (FR-030, FR-031, FR-032).

    Args:
        response_id: Response message ID
        current_user: Current authenticated user
        share_service: Share service dependency

    Returns:
        Share URL and preview image URL
    """
    try:
        share_url = await share_service.generate_share_url(
            response_id=response_id,
            user_id=current_user.id,
        )

        # Increment share count
        await share_service.increment_share_count(response_id)

        # Update user stats
        from ..services.user_service import UserService

        user_service = UserService(share_service.db)
        await user_service.increment_stats(
            user_id=current_user.id,
            stat_name="total_shares",
        )

        logger.info(f"User {current_user.email} shared response {response_id}")

        return {
            "message": "Share link generated!",
            "share_url": share_url,
            "preview_image": f"{share_url}/preview.png",
        }

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
