"""Daily challenge routes (FR-033, FR-034, FR-019)."""

import logging

from bson import ObjectId
from fastapi import APIRouter, HTTPException, status

from ..core.dependencies import ChallengeServiceDep, CurrentUserDep
from ..schemas.challenges import (
    ChallengeCompleteRequest,
    DailyChallengeResponse,
)

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/today", response_model=DailyChallengeResponse)
async def get_today_challenge(
    challenge_service: ChallengeServiceDep,
) -> DailyChallengeResponse:
    """
    Get today's daily challenge (FR-033).

    Args:
        challenge_service: Challenge service dependency

    Returns:
        Today's daily challenge
    """
    challenge = await challenge_service.get_today_challenge()

    if not challenge:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Today's challenge not available yet. Check back soon!",
        )

    return DailyChallengeResponse(
        id=str(challenge.id),
        challenge_id=challenge.challenge_id,
        date=challenge.date,
        prompt_text=challenge.prompt_text,
        theme_category=challenge.theme_category,
        difficulty=challenge.difficulty,
        completion_count=challenge.completion_count,
    )


@router.post("/{challenge_id}/complete", status_code=status.HTTP_200_OK)
async def complete_challenge(
    challenge_id: str,
    completion_data: ChallengeCompleteRequest,
    current_user: CurrentUserDep,
    challenge_service: ChallengeServiceDep,
) -> dict:
    """
    Mark daily challenge as completed (FR-034, FR-019).

    Updates streak count and unlocks themes after 7-day streak.

    Args:
        challenge_id: Challenge ID (YYYYMMDD format)
        completion_data: Completion request data
        current_user: Current authenticated user
        challenge_service: Challenge service dependency

    Returns:
        Success message with streak info and unlocked themes

    Raises:
        HTTPException: If challenge not found or already completed
    """
    try:
        result = await challenge_service.complete_challenge(
            challenge_id=challenge_id,
            user_id=current_user.id,
            response_text=completion_data.response_text,
        )

        # Check for theme unlocks
        unlocked_themes = result.get("unlocked_themes", [])
        streak_count = result.get("streak_count", 0)

        message = f"Challenge completed! Your streak is now {streak_count} days!"

        if unlocked_themes:
            theme_names = ", ".join(unlocked_themes)
            message += f" You've unlocked new themes: {theme_names}!"

        logger.info(
            f"User {current_user.email} completed challenge {challenge_id} "
            f"(streak: {streak_count})"
        )

        return {
            "message": message,
            "streak_count": streak_count,
            "unlocked_themes": unlocked_themes,
        }

    except ValueError as e:
        error_msg = str(e)
        if "already completed" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="You've already completed today's challenge! Come back tomorrow for a new one.",
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg,
            )


@router.get("/history", response_model=list[DailyChallengeResponse])
async def get_challenge_history(
    current_user: CurrentUserDep,
    challenge_service: ChallengeServiceDep,
) -> list[DailyChallengeResponse]:
    """
    Get user's challenge completion history.

    Args:
        current_user: Current authenticated user
        challenge_service: Challenge service dependency

    Returns:
        List of completed challenges
    """
    # This would require a user_challenge_completions collection
    # For now, return empty list as placeholder
    return []
