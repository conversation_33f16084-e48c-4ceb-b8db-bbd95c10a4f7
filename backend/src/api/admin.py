"""Admin routes for moderation (FR-040)."""

import logging

from fastapi import APIRouter, HTTPException, Query, status

from ..core.dependencies import AdminUserDep, ModerationServiceDep
from ..schemas.admin import FlaggedContentSchema

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/moderation/flagged", response_model=list[FlaggedContentSchema])
async def get_flagged_content(
    admin_user: AdminUserDep,
    moderation_service: ModerationServiceDep,
    limit: int = Query(50, ge=1, le=100, description="Number of entries"),
    offset: int = Query(0, ge=0, description="Pagination offset"),
    status_filter: str | None = Query(
        None,
        description="Filter by status: pending, approved, rejected",
        pattern="^(pending|approved|rejected)$",
    ),
) -> list[FlaggedContentSchema]:
    """
    Get flagged content for admin review (FR-040).

    Args:
        admin_user: Current admin user
        moderation_service: Moderation service dependency
        limit: Number of entries to return
        offset: Pagination offset
        status_filter: Optional status filter

    Returns:
        List of flagged content entries

    Raises:
        HTTPException: If not admin or query fails
    """
    try:
        flagged_content = await moderation_service.get_flagged_content(
            limit=limit,
            offset=offset,
            status_filter=status_filter,
        )

        logger.info(
            f"Admin {admin_user.email} accessed flagged content "
            f"(limit: {limit}, offset: {offset}, filter: {status_filter})"
        )

        return [
            FlaggedContentSchema(
                id=str(item["_id"]),
                user_id=str(item["user_id"]),
                content_type=item["content_type"],
                content_text=item["content_text"],
                matched_keywords=item["matched_keywords"],
                status=item.get("status", "pending"),
                flagged_at=item["flagged_at"],
                reviewed_at=item.get("reviewed_at"),
                reviewed_by=str(item["reviewed_by"]) if item.get("reviewed_by") else None,
            )
            for item in flagged_content
        ]

    except Exception as e:
        logger.error(f"Error fetching flagged content: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch flagged content",
        )


@router.post("/moderation/{flagged_id}/approve", status_code=status.HTTP_200_OK)
async def approve_content(
    flagged_id: str,
    admin_user: AdminUserDep,
    moderation_service: ModerationServiceDep,
) -> dict[str, str]:
    """
    Approve flagged content (FR-040).

    Args:
        flagged_id: Flagged content ID
        admin_user: Current admin user
        moderation_service: Moderation service dependency

    Returns:
        Success message

    Raises:
        HTTPException: If not admin or update fails
    """
    try:
        await moderation_service.update_flag_status(
            flag_id=flagged_id,
            new_status="approved",
            reviewed_by=admin_user.id,
        )

        logger.info(
            f"Admin {admin_user.email} approved flagged content {flagged_id}"
        )

        return {"message": "Content approved successfully"}

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )


@router.post("/moderation/{flagged_id}/reject", status_code=status.HTTP_200_OK)
async def reject_content(
    flagged_id: str,
    admin_user: AdminUserDep,
    moderation_service: ModerationServiceDep,
) -> dict[str, str]:
    """
    Reject flagged content (FR-040).

    Args:
        flagged_id: Flagged content ID
        admin_user: Current admin user
        moderation_service: Moderation service dependency

    Returns:
        Success message

    Raises:
        HTTPException: If not admin or update fails
    """
    try:
        await moderation_service.update_flag_status(
            flag_id=flagged_id,
            new_status="rejected",
            reviewed_by=admin_user.id,
        )

        logger.info(
            f"Admin {admin_user.email} rejected flagged content {flagged_id}"
        )

        return {"message": "Content rejected successfully"}

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )


@router.get("/stats", response_model=dict)
async def get_admin_stats(
    admin_user: AdminUserDep,
    moderation_service: ModerationServiceDep,
) -> dict:
    """
    Get admin dashboard statistics.

    Args:
        admin_user: Current admin user
        moderation_service: Moderation service dependency

    Returns:
        Dashboard statistics
    """
    try:
        stats = await moderation_service.get_moderation_stats()

        logger.info(f"Admin {admin_user.email} accessed dashboard stats")

        return stats

    except Exception as e:
        logger.error(f"Error fetching admin stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch statistics",
        )


@router.post("/users/{user_id}/ban", status_code=status.HTTP_200_OK)
async def ban_user(
    user_id: str,
    admin_user: AdminUserDep,
    moderation_service: ModerationServiceDep,
) -> dict[str, str]:
    """
    Ban user (placeholder for future implementation).

    Args:
        user_id: User ID to ban
        admin_user: Current admin user
        moderation_service: Moderation service dependency

    Returns:
        Success message
    """
    logger.warning(
        f"Admin {admin_user.email} attempted to ban user {user_id} "
        "(ban feature not yet implemented)"
    )

    return {
        "message": "User ban feature not yet implemented. "
        "Contact system administrator for manual action."
    }
