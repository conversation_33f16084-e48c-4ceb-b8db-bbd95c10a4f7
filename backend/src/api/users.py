"""User profile routes (FR-007, FR-041)."""

import logging

from fastapi import APIRouter, HTTPException, status

from .auth import build_user_profile
from ..core.dependencies import CurrentUserDep, UserServiceDep
from ..schemas.auth import UpdateProfileRequest, UserProfile

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/me", response_model=UserProfile)
async def get_profile(
    current_user: CurrentUserDep,
) -> UserProfile:
    """
    Get current user profile (FR-007).

    Args:
        current_user: Current authenticated user

    Returns:
        User profile
    """
    return build_user_profile(current_user)


@router.patch("/me", response_model=UserProfile)
async def update_profile(
    update_data: UpdateProfileRequest,
    current_user: CurrentUserDep,
    user_service: UserServiceDep,
) -> UserProfile:
    """
    Update user profile (FR-007).

    Args:
        update_data: Profile update data
        current_user: Current authenticated user
        user_service: User service dependency

    Returns:
        Updated user profile

    Raises:
        HTTPException: If update fails
    """
    try:
        updated_user = await user_service.update_profile(
            user_id=current_user.id,
            display_name=update_data.display_name,
            avatar_url=update_data.avatar_url,
            preferences=update_data.preferences,
        )

        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update profile",
            )

        logger.info(f"User profile updated: {current_user.email}")

        return build_user_profile(updated_user)

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )


@router.delete("/me", status_code=status.HTTP_200_OK)
async def delete_account(
    current_user: CurrentUserDep,
    user_service: UserServiceDep,
) -> dict[str, str]:
    """
    Delete user account with cascade (FR-041 GDPR/LGPD).

    Args:
        current_user: Current authenticated user
        user_service: User service dependency

    Returns:
        Success message

    Raises:
        HTTPException: If deletion fails
    """
    try:
        deleted = await user_service.delete_user_cascade(current_user.id)

        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete account",
            )

        logger.info(f"User account deleted (GDPR): {current_user.email}")

        return {
            "message": "Your account and all associated data have been permanently deleted. "
            "We're sorry to see you go!"
        }

    except Exception as e:
        logger.error(f"Error deleting user account: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete account. Please contact support.",
        )
