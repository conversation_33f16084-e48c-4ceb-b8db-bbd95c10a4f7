"""Dependency injection for FastAPI routes."""

from typing import Annotated

from bson import ObjectId
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from motor.motor_asyncio import AsyncIOMotorDatabase

from ..models.user import UserInDB
from ..services.ai_service import AIService
from ..services.auth_service import AuthService
from ..services.challenge_service import ChallengeService
from ..services.chat_service import ChatService
from ..services.favorites_service import FavoritesService
from ..services.leaderboard_service import LeaderboardService
from ..services.moderation_service import ModerationService
from ..services.prompt_service import PromptService
from ..services.room_service import RoomService
from ..services.share_service import ShareService
from ..services.user_service import UserService
from .database import Database

# Security scheme for JWT Bearer tokens
# TEMPORARY DEV MODE: Make security optional
import os
security = HTTPBearer(auto_error=not (os.getenv("DEV_MODE") == "true"))


async def get_database() -> AsyncIOMotorDatabase:
    """
    Get database dependency.

    Returns:
        AsyncIOMotorDatabase instance

    Raises:
        HTTPException: If database not initialized
    """
    if Database.db is None:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Database not available",
        )
    return Database.db


async def get_auth_service(
    db: Annotated[AsyncIOMotorDatabase, Depends(get_database)],
) -> AuthService:
    """Get auth service dependency."""
    return AuthService(db)


async def get_user_service(
    db: Annotated[AsyncIOMotorDatabase, Depends(get_database)],
) -> UserService:
    """Get user service dependency."""
    return UserService(db)


async def get_chat_service(
    db: Annotated[AsyncIOMotorDatabase, Depends(get_database)],
) -> ChatService:
    """Get chat service dependency."""
    return ChatService(db)


async def get_room_service(
    db: Annotated[AsyncIOMotorDatabase, Depends(get_database)],
) -> RoomService:
    """Get room service dependency."""
    return RoomService(db)


async def get_challenge_service(
    db: Annotated[AsyncIOMotorDatabase, Depends(get_database)],
) -> ChallengeService:
    """Get challenge service dependency."""
    return ChallengeService(db)


async def get_prompt_service(
    db: Annotated[AsyncIOMotorDatabase, Depends(get_database)],
) -> PromptService:
    """Get prompt service dependency."""
    return PromptService(db)


async def get_leaderboard_service(
    db: Annotated[AsyncIOMotorDatabase, Depends(get_database)],
) -> LeaderboardService:
    """Get leaderboard service dependency."""
    return LeaderboardService(db)


async def get_moderation_service(
    db: Annotated[AsyncIOMotorDatabase, Depends(get_database)],
) -> ModerationService:
    """Get moderation service dependency."""
    return ModerationService(db)


async def get_favorites_service(
    db: Annotated[AsyncIOMotorDatabase, Depends(get_database)],
) -> FavoritesService:
    """Get favorites service dependency."""
    return FavoritesService(db)


async def get_share_service(
    db: Annotated[AsyncIOMotorDatabase, Depends(get_database)],
) -> ShareService:
    """Get share service dependency."""
    return ShareService(db)


async def get_ai_service() -> AIService:
    """
    Get AI service dependency.

    Returns:
        AIService instance

    Raises:
        HTTPException: If AI service not initialized
    """
    from ..main import get_ai_service as get_global_ai_service

    try:
        return get_global_ai_service()
    except RuntimeError:
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="AI service not available",
        )


async def get_current_user(
    credentials: Annotated[HTTPAuthorizationCredentials | None, Depends(security)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)],
) -> UserInDB:
    """
    Get current authenticated user from JWT token.

    Args:
        credentials: HTTP Bearer credentials with JWT token
        auth_service: Auth service for token verification

    Returns:
        Current authenticated user

    Raises:
        HTTPException: If token invalid or user not found
    """
    # TEMPORARY DEV MODE: Skip authentication
    import os
    if os.getenv("DEV_MODE") == "true" and not credentials:
        from ..models.user import UserPreferences
        from datetime import datetime, UTC
        return UserInDB(
            id=ObjectId("507f1f77bcf86cd799439011"),
            email="<EMAIL>",
            display_name="Dev User",
            hashed_password="",
            role="user",
            is_verified=True,
            created_at=datetime.now(UTC),
            preferences=UserPreferences(humor_style="random")
        )

    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated",
            headers={"WWW-Authenticate": "Bearer"},
        )

    token = credentials.credentials

    # Verify token and get user_id
    user_id = auth_service.verify_token(token, token_type="access")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired token",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Get user from database
    user = await auth_service.get_user_by_id(user_id)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return user


async def get_optional_user(
    credentials: Annotated[HTTPAuthorizationCredentials | None, Depends(security)],
    auth_service: Annotated[AuthService, Depends(get_auth_service)],
) -> UserInDB | None:
    """
    Get current user if authenticated, None otherwise.

    Used for optional authentication endpoints.

    Args:
        credentials: Optional HTTP Bearer credentials
        auth_service: Auth service for token verification

    Returns:
        Current user if authenticated, None otherwise
    """
    if not credentials:
        return None

    try:
        user_id = auth_service.verify_token(credentials.credentials, token_type="access")
        if not user_id:
            return None

        return await auth_service.get_user_by_id(user_id)
    except Exception:
        return None


async def require_admin(
    current_user: Annotated[UserInDB, Depends(get_current_user)],
) -> UserInDB:
    """
    Require admin role for route access.

    Args:
        current_user: Current authenticated user

    Returns:
        Current user if admin

    Raises:
        HTTPException: If user not admin
    """
    # Check if user has admin role
    if not current_user.role == "admin":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Admin access required",
        )

    return current_user


# Type aliases for common dependencies
DbDep = Annotated[AsyncIOMotorDatabase, Depends(get_database)]
AuthServiceDep = Annotated[AuthService, Depends(get_auth_service)]
UserServiceDep = Annotated[UserService, Depends(get_user_service)]
ChatServiceDep = Annotated[ChatService, Depends(get_chat_service)]
RoomServiceDep = Annotated[RoomService, Depends(get_room_service)]
ChallengeServiceDep = Annotated[ChallengeService, Depends(get_challenge_service)]
PromptServiceDep = Annotated[PromptService, Depends(get_prompt_service)]
LeaderboardServiceDep = Annotated[LeaderboardService, Depends(get_leaderboard_service)]
ModerationServiceDep = Annotated[ModerationService, Depends(get_moderation_service)]
FavoritesServiceDep = Annotated[FavoritesService, Depends(get_favorites_service)]
ShareServiceDep = Annotated[ShareService, Depends(get_share_service)]
AIServiceDep = Annotated[AIService, Depends(get_ai_service)]
CurrentUserDep = Annotated[UserInDB, Depends(get_current_user)]
OptionalUserDep = Annotated[UserInDB | None, Depends(get_optional_user)]
AdminUserDep = Annotated[UserInDB, Depends(require_admin)]
