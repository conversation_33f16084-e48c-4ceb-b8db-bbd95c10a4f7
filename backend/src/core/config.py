import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    """Application settings with environment variables and defaults."""

    # Database
    MONGODB_URL: str = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
    DATABASE_NAME: str = os.getenv("DATABASE_NAME", "hallucination_station")

    # AI Model
    AI_MODEL_PATH: str = os.getenv("AI_MODEL_PATH", "./models/phi-4-mini-instruct.gguf")
    AI_MODEL_CONTEXT_SIZE: int = int(os.getenv("AI_MODEL_CONTEXT_SIZE", "4096"))
    AI_MODEL_TIMEOUT: float = float(os.getenv("AI_MODEL_TIMEOUT", "2.0"))

    # Authentication
    SECRET_KEY: str = os.getenv("SECRET_KEY", "dev-secret-key-replace-in-production")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "15"))
    REFRESH_TOKEN_EXPIRE_DAYS: int = int(os.getenv("REFRESH_TOKEN_EXPIRE_DAYS", "7"))

    # API Settings
    RATE_LIMIT_PER_MINUTE: int = int(os.getenv("RATE_LIMIT_PER_MINUTE", "10"))

    # Environment
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    ENVIRONMENT: str = os.getenv("ENVIRONMENT", "development")

# Create settings instance for import
settings = Settings()