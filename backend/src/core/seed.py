"""Seed initial data for the application."""

import asyncio
import os
from datetime import datetime

from motor.motor_asyncio import AsyncIOMotorClient

from ..models.user import User, UserConsent


async def create_admin_user(db):
    """Create an initial admin user for testing."""
    users_collection = db.users

    # Check if admin already exists
    admin_email = os.environ.get("ADMIN_EMAIL", "<EMAIL>")
    existing_admin = await users_collection.find_one({"email": admin_email})

    if existing_admin:
        print(f"Admin user {admin_email} already exists")
        return

    # Create admin user
    from ..services.auth_service import pwd_context

    admin_password = os.environ.get("ADMIN_PASSWORD", "admin123")  # Default for development only
    admin_user = User(
        email=admin_email,
        password_hash=pwd_context.hash(admin_password),
        display_name="Admin",
        email_verified=True,
        role="admin",  # Admin role for access control
        consent=UserConsent(
            given=True,
            date=datetime.utcnow(),
            ip_address="127.0.0.1"
        )
    )

    # Insert to database
    await users_collection.insert_one(
        admin_user.model_dump(by_alias=True, exclude={"id"})
    )
    print(f"Created admin user: {admin_email}")


async def seed_data():
    """Seed initial data for the application."""
    # Connect to MongoDB
    mongodb_url = os.environ.get("MONGODB_URL", "mongodb://localhost:27017")
    client = AsyncIOMotorClient(mongodb_url)
    db = client.hallucination_station

    # Create admin user
    await create_admin_user(db)


def run_seed():
    """Run the seed function."""
    print("Seeding initial data...")
    asyncio.run(seed_data())
    print("Seeding completed")


if __name__ == "__main__":
    run_seed()