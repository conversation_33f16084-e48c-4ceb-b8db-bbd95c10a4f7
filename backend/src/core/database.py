"""Database initialization and connection management."""

from typing import As<PERSON><PERSON><PERSON>ator, Optional

from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase

from .logging import get_logger

logger = get_logger(__name__)


class Database:
    """MongoDB database manager using Motor async driver."""

    client: Optional[AsyncIOMotorClient] = None
    db: Optional[AsyncIOMotorDatabase] = None

    @classmethod
    async def connect_db(cls, mongodb_url: str, database_name: str) -> None:
        """Connect to MongoDB and create indexes."""
        logger.info("database_connecting", database=database_name, url=mongodb_url)
        cls.client = AsyncIOMotorClient(mongodb_url)
        cls.db = cls.client[database_name]

        # Create indexes
        await cls.create_indexes()
        logger.info("database_connected", database=database_name)

    @classmethod
    async def close_db(cls) -> None:
        """Close MongoDB connection."""
        if cls.client:
            cls.client.close()
            logger.info("database_connection_closed")

    @classmethod
    async def create_indexes(cls) -> None:
        """Create all required indexes for collections."""
        if cls.db is None:
            raise RuntimeError("Database not connected")

        # Users collection indexes
        await cls.db.users.create_index("email", unique=True)
        await cls.db.users.create_index("social_providers.google.provider_id", sparse=True)
        await cls.db.users.create_index("social_providers.facebook.provider_id", sparse=True)
        await cls.db.users.create_index([("streak_count", -1)])
        await cls.db.users.create_index([("created_at", -1)])

        # ChatSessions collection indexes
        await cls.db.chat_sessions.create_index("user_id")
        await cls.db.chat_sessions.create_index([("user_id", 1), ("session_start", -1)])
        await cls.db.chat_sessions.create_index("is_active")
        await cls.db.chat_sessions.create_index([("session_start", -1)])

        # GameRooms collection indexes
        await cls.db.game_rooms.create_index("room_code", unique=True)
        await cls.db.game_rooms.create_index("creator_user_id")
        await cls.db.game_rooms.create_index("status")
        await cls.db.game_rooms.create_index(
            "expires_at", expireAfterSeconds=0
        )  # TTL index
        await cls.db.game_rooms.create_index([("status", 1), ("created_at", -1)])

        # DailyChallenges collection indexes
        await cls.db.daily_challenges.create_index("challenge_id", unique=True)
        await cls.db.daily_challenges.create_index([("date", -1)])

        # PromptLibrary collection indexes
        await cls.db.prompt_library.create_index("category")
        await cls.db.prompt_library.create_index("theme_id")
        await cls.db.prompt_library.create_index("is_themed")

        # LeaderboardCache collection indexes
        await cls.db.leaderboard_cache.create_index(
            "expires_at", expireAfterSeconds=0
        )  # TTL index
        await cls.db.leaderboard_cache.create_index("cache_type")

        logger.info("All indexes created successfully")

    @classmethod
    async def seed_prompt_library(cls) -> None:
        """Seed prompt library with initial data."""
        if cls.db is None:
            raise RuntimeError("Database not connected")

        # Check if already seeded
        count = await cls.db.prompt_library.count_documents({})
        if count > 0:
            logger.info(f"Prompt library already seeded with {count} prompts")
            return

        seed_prompts = [
            # Science
            {
                "category": "science",
                "question_text": "Why is the sky blue?",
                "difficulty": "easy",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "science",
                "question_text": "What causes earthquakes?",
                "difficulty": "medium",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "science",
                "question_text": "How many planets are in our solar system?",
                "difficulty": "easy",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "science",
                "question_text": "What is the speed of light?",
                "difficulty": "medium",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "science",
                "question_text": "How do magnets work?",
                "difficulty": "medium",
                "is_themed": False,
                "unlock_criteria": None,
            },
            # History
            {
                "category": "history",
                "question_text": "Who built the pyramids of Egypt?",
                "difficulty": "easy",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "history",
                "question_text": "When did World War II end?",
                "difficulty": "medium",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "history",
                "question_text": "Who discovered America?",
                "difficulty": "easy",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "history",
                "question_text": "What was the first civilization?",
                "difficulty": "hard",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "history",
                "question_text": "When was the Roman Empire founded?",
                "difficulty": "medium",
                "is_themed": False,
                "unlock_criteria": None,
            },
            # Technology
            {
                "category": "technology",
                "question_text": "How do computers work?",
                "difficulty": "medium",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "technology",
                "question_text": "Who invented the internet?",
                "difficulty": "medium",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "technology",
                "question_text": "What does CPU stand for?",
                "difficulty": "easy",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "technology",
                "question_text": "How do smartphones detect your location?",
                "difficulty": "medium",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "technology",
                "question_text": "What is artificial intelligence?",
                "difficulty": "hard",
                "is_themed": False,
                "unlock_criteria": None,
            },
            # Sports
            {
                "category": "sports",
                "question_text": "How many players are on a soccer team?",
                "difficulty": "easy",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "sports",
                "question_text": "What is the fastest ball sport?",
                "difficulty": "medium",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "sports",
                "question_text": "Who won the most Olympic gold medals?",
                "difficulty": "hard",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "sports",
                "question_text": "What sport is played at Wimbledon?",
                "difficulty": "easy",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "sports",
                "question_text": "How long is a marathon?",
                "difficulty": "medium",
                "is_themed": False,
                "unlock_criteria": None,
            },
            # Entertainment
            {
                "category": "entertainment",
                "question_text": "Who is the most famous fictional detective?",
                "difficulty": "easy",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "entertainment",
                "question_text": "Which movie won the first Academy Award?",
                "difficulty": "hard",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "entertainment",
                "question_text": "What is the highest-grossing movie of all time?",
                "difficulty": "medium",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "entertainment",
                "question_text": "Who wrote Harry Potter?",
                "difficulty": "easy",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "entertainment",
                "question_text": "What is the longest-running TV show?",
                "difficulty": "hard",
                "is_themed": False,
                "unlock_criteria": None,
            },
            # Food & Cooking
            {
                "category": "food",
                "question_text": "What country invented pizza?",
                "difficulty": "easy",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "food",
                "question_text": "What is the main ingredient in guacamole?",
                "difficulty": "easy",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "food",
                "question_text": "How is chocolate made?",
                "difficulty": "medium",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "food",
                "question_text": "What is the most expensive spice?",
                "difficulty": "hard",
                "is_themed": False,
                "unlock_criteria": None,
            },
            {
                "category": "food",
                "question_text": "What temperature does water boil at?",
                "difficulty": "easy",
                "is_themed": False,
                "unlock_criteria": None,
            },
        ]

        await cls.db.prompt_library.insert_many(seed_prompts)
        logger.info(f"Seeded {len(seed_prompts)} prompts to library")


async def get_database() -> AsyncGenerator[AsyncIOMotorDatabase, None]:
    """Dependency for getting database instance."""
    if Database.db is None:
        raise RuntimeError("Database not initialized")
    yield Database.db
