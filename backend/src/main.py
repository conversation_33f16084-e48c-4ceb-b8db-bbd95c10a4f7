"""Main FastAPI application with startup/shutdown events."""

import os
import yaml
from contextlib import asynccontextmanager
from pathlib import Path
from typing import AsyncGenerator, Dict, Any

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi

from .api import admin, auth, challenges, chat, leaderboard, prompts, rooms, users
from .api.error_handlers import (
    http_exception_handler,
    moderation_exception_handler,
    validation_exception_handler,
)
from .api.middleware import RateLimitMiddleware, RequestLoggingMiddleware
from .api.websocket import websocket_endpoint
from .core.database import Database
from .core.logging import configure_logging, get_logger
from .core.seed import create_admin_user
from .services.ai_service import AIService

# Configure structured logging
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
JSON_LOGS = os.getenv("JSON_LOGS", "false").lower() == "true"
configure_logging(log_level=LOG_LEVEL, json_logs=JSON_LOGS)

logger = get_logger(__name__)

# Configuration from environment
MONGODB_URL = os.getenv("MONGODB_URL", "mongodb://localhost:27017")
DATABASE_NAME = os.getenv("DATABASE_NAME", "hallucination_station")
AI_MODEL_PATH = os.getenv("AI_MODEL_PATH", "./models/phi-4-mini-instruct.gguf")

# Global AI service instance
ai_service: AIService | None = None


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Lifespan context manager for startup and shutdown events.

    Startup:
    - Connect to MongoDB
    - Create indexes
    - Seed prompt library
    - Load AI model

    Shutdown:
    - Close MongoDB connection
    """
    global ai_service

    logger.info("application_starting", mongodb_url=MONGODB_URL, database=DATABASE_NAME)

    # Connect to MongoDB
    try:
        await Database.connect_db(MONGODB_URL, DATABASE_NAME)
        logger.info("mongodb_connected", database=DATABASE_NAME)
    except Exception as e:
        logger.error("mongodb_connection_failed", error=str(e), exc_info=True)
        raise

    # Seed prompt library (if needed)
    try:
        await Database.seed_prompt_library()
        logger.info("prompt_library_seeded")
    except Exception as e:
        logger.warning("prompt_library_seed_failed", error=str(e))

    # Seed admin user (if needed)
    try:
        await create_admin_user(Database.db)
        logger.info("admin_user_seeded")
    except Exception as e:
        logger.warning("admin_user_seed_failed", error=str(e))

    # Initialize and load AI model
    try:
        ai_service = AIService(
            model_path=AI_MODEL_PATH,
            n_ctx=512,
            n_threads=os.cpu_count() or 4,
        )
        await ai_service.load_model()
        logger.info("ai_service_initialized", model_path=AI_MODEL_PATH)
    except Exception as e:
        logger.error("ai_service_initialization_failed", error=str(e), exc_info=True)
        # Don't raise - allow app to start without AI for testing
        ai_service = None

    logger.info("application_startup_complete")

    yield

    # Shutdown
    logger.info("application_shutting_down")
    await Database.close_db()
    logger.info("application_shutdown_complete")


# Create FastAPI application
app = FastAPI(
    title="Natural Hallucination API",
    description="AI-powered entertainment app providing intentionally wrong, humorous answers",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc",
)

# Custom OpenAPI schema from api-contract.yaml
def custom_openapi() -> Dict[str, Any]:
    """Generate custom OpenAPI schema from api-contract.yaml."""
    if app.openapi_schema:
        return app.openapi_schema

    # Default schema generation
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )

    # Try to load api-contract.yaml for enhanced docs
    contract_path = Path(__file__).parents[3] / "specs" / "001-build-an-application" / "contracts" / "api-contract.yaml"
    try:
        if contract_path.exists():
            with open(contract_path, "r") as f:
                contract_yaml = yaml.safe_load(f)

            # Merge components from the contract
            if "components" in contract_yaml:
                if "schemas" in contract_yaml["components"]:
                    openapi_schema.setdefault("components", {}).setdefault("schemas", {})
                    openapi_schema["components"]["schemas"].update(contract_yaml["components"]["schemas"])

                if "securitySchemes" in contract_yaml["components"]:
                    openapi_schema.setdefault("components", {}).setdefault("securitySchemes", {})
                    openapi_schema["components"]["securitySchemes"].update(contract_yaml["components"]["securitySchemes"])

            # Add tags from the contract
            if "tags" in contract_yaml:
                openapi_schema["tags"] = contract_yaml["tags"]

            # Add server information
            if "servers" in contract_yaml:
                openapi_schema["servers"] = contract_yaml["servers"]

            logger.info("openapi_schema_enhanced", contract_path=str(contract_path))
        else:
            logger.warning("api_contract_not_found", path=str(contract_path))
    except Exception as e:
        logger.error("api_contract_load_failed", error=str(e), exc_info=True)

    app.openapi_schema = openapi_schema
    return app.openapi_schema

# Set the custom OpenAPI schema
app.openapi = custom_openapi

# CORS configuration
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",  # Frontend dev server
        "http://localhost:5173",  # Vite dev server
        os.getenv("FRONTEND_URL", "http://localhost:3000"),
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request logging middleware
app.add_middleware(RequestLoggingMiddleware)

# Rate limiting middleware
app.add_middleware(RateLimitMiddleware, max_requests=10, window_seconds=60)

# Register exception handlers
app.add_exception_handler(ValueError, validation_exception_handler)
app.add_exception_handler(Exception, http_exception_handler)

# Register routers
app.include_router(auth.router, prefix="/api/auth", tags=["auth"])
app.include_router(users.router, prefix="/api/users", tags=["users"])
app.include_router(chat.router, prefix="/api/chat", tags=["chat"])
app.include_router(rooms.router, prefix="/api/rooms", tags=["rooms"])
app.include_router(challenges.router, prefix="/api/challenges", tags=["challenges"])
app.include_router(prompts.router, prefix="/api/prompts", tags=["prompts"])
app.include_router(leaderboard.router, prefix="/api/leaderboard", tags=["leaderboard"])
app.include_router(admin.router, prefix="/api/admin", tags=["admin"])

# WebSocket endpoint
app.add_websocket_route("/ws", websocket_endpoint)


@app.get("/", tags=["health"])
async def root() -> dict[str, str]:
    """Root endpoint - health check."""
    return {
        "status": "healthy",
        "service": "Natural Hallucination API",
        "version": "1.0.0",
    }


@app.get("/health", tags=["health"])
async def health_check() -> dict[str, str | bool]:
    """Health check endpoint."""
    db_connected = Database.db is not None
    ai_loaded = ai_service is not None and ai_service.llm is not None

    return {
        "status": "healthy" if db_connected else "degraded",
        "database": "connected" if db_connected else "disconnected",
        "ai_service": "loaded" if ai_loaded else "not loaded",
    }


def get_ai_service() -> AIService:
    """Get global AI service instance."""
    if ai_service is None:
        raise RuntimeError("AI service not initialized")
    return ai_service
