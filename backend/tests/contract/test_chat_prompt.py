import pytest
from fastapi.testclient import TestClient
import time

# This is a placeholder for the main app
# from backend.src.main import app

# client = TestClient(app)

@pytest.mark.xfail(reason="Implementation not yet complete")
def test_submit_prompt():
    # # First, login and create a session
    # login_response = client.post("/api/auth/login", data={
    #     "username": "<EMAIL>",
    #     "password": "password123"
    # })
    # access_token = login_response.json()["access_token"]
    # session_response = client.post("/api/chat/sessions", headers={
    #     "Authorization": f"Bearer {access_token}"
    # }, json={})
    # session_id = session_response.json()["id"]
    
    # # Submit a prompt
    # start_time = time.time()
    # response = client.post("/api/chat/prompt", headers={
    #     "Authorization": f"Bearer {access_token}"
    # }, json={
    #     "session_id": session_id,
    #     "prompt": "What is the meaning of life?"
    # })
    # end_time = time.time()
    
    # # Assertions
    # assert response.status_code == 200
    # data = response.json()
    # assert "response" in data
    # assert end_time - start_time < 2 # FR-010
    assert False
