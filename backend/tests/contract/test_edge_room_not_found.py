import pytest
from fastapi.testclient import TestClient

# This is a placeholder for the main app
# from backend.src.main import app

# client = TestClient(app)

@pytest.mark.xfail(reason="Implementation not yet complete")
def test_room_not_found():
    # # First, login to get an access token
    # login_response = client.post("/api/auth/login", data={
    #     "username": "<EMAIL>",
    #     "password": "password123"
    # })
    # access_token = login_response.json()["access_token"]
    
    # # Try to get a non-existent room
    # response = client.get("/api/rooms/nonexistent", headers={
    #     "Authorization": f"Bearer {access_token}"
    # })
    # assert response.status_code == 404
    # assert "detail" in response.json()
    assert False
