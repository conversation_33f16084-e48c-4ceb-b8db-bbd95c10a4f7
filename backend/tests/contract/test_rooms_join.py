import pytest
from fastapi.testclient import TestClient

# This is a placeholder for the main app
# from backend.src.main import app

# client = TestClient(app)

@pytest.mark.xfail(reason="Implementation not yet complete")
def test_join_room():
    # # First, login and create a room to get a room code
    # login_response = client.post("/api/auth/login", data={
    #     "username": "<EMAIL>",
    #     "password": "password123"
    # })
    # access_token = login_response.json()["access_token"]
    # room_response = client.post("/api/rooms", headers={
    #     "Authorization": f"Bearer {access_token}"
    # })
    # room_code = room_response.json()["room_code"]
    
    # # Join the room
    # response = client.post(f"/api/rooms/{room_code}/join", headers={
    #     "Authorization": f"Bearer {access_token}"
    # })
    # assert response.status_code == 200
    # assert response.json()["message"] == "Successfully joined the room."
    assert False
