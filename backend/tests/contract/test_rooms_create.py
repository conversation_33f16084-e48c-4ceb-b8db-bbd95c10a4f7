import pytest
from fastapi.testclient import TestClient

# This is a placeholder for the main app
# from backend.src.main import app

# client = TestClient(app)

@pytest.mark.xfail(reason="Implementation not yet complete")
def test_create_room():
    # # First, login to get an access token
    # login_response = client.post("/api/auth/login", data={
    #     "username": "<EMAIL>",
    #     "password": "password123"
    # })
    # access_token = login_response.json()["access_token"]
    
    # # Create a room
    # response = client.post("/api/rooms", headers={
    #     "Authorization": f"Bearer {access_token}"
    # })
    # assert response.status_code == 201
    # data = response.json()
    # assert "room_code" in data
    # assert len(data["room_code"]) == 6
    assert False
