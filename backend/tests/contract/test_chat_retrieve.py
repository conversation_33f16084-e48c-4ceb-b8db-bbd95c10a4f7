import pytest
from fastapi.testclient import TestClient

# This is a placeholder for the main app
# from backend.src.main import app

# client = TestClient(app)

@pytest.mark.xfail(reason="Implementation not yet complete")
def test_get_chat_sessions():
    # # First, login to get an access token
    # login_response = client.post("/api/auth/login", data={
    #     "username": "<EMAIL>",
    #     "password": "password123"
    # })
    # access_token = login_response.json()["access_token"]
    
    # # Get chat sessions
    # response = client.get("/api/chat/sessions", headers={
    #     "Authorization": f"Bearer {access_token}"
    # })
    # assert response.status_code == 200
    # data = response.json()
    # assert isinstance(data, list)
    assert False

@pytest.mark.xfail(reason="Implementation not yet complete")
def test_get_chat_session_by_id():
    # # First, login and create a session to get a session id
    # login_response = client.post("/api/auth/login", data={
    #     "username": "<EMAIL>",
    #     "password": "password123"
    # })
    # access_token = login_response.json()["access_token"]
    # session_response = client.post("/api/chat/sessions", headers={
    #     "Authorization": f"Bearer {access_token}"
    # }, json={})
    # session_id = session_response.json()["id"]
    
    # # Get the chat session by id
    # response = client.get(f"/api/chat/sessions/{session_id}", headers={
    #     "Authorization": f"Bearer {access_token}"
    # })
    # assert response.status_code == 200
    # data = response.json()
    # assert data["id"] == session_id
    assert False
