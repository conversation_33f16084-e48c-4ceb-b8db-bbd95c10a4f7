"""Integration test for favorites and sharing (Scenario 10 from quickstart.md).

This test validates FR-030 to FR-032, FR-035 - favorite response, share to social, copy link.
"""

from datetime import datetime

import pytest
from bson import ObjectId
from fastapi import status
from fastapi.testclient import TestClient


@pytest.mark.asyncio
async def test_favorite_response_flow(test_client: TestClient, test_user, auth_headers, mongodb):
    """Test favoriting responses: chat → favorite response → view favorites."""
    # Step 1: Create a chat session
    create_session_response = test_client.post(
        "/api/chat/sessions",
        headers=auth_headers,
        json={"mode": "solo", "humor_style": "absurd"},
    )

    assert create_session_response.status_code == status.HTTP_201_CREATED
    session_id = create_session_response.json()["id"]

    # Step 2: Submit a prompt to get a response
    prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": "What's the funniest animal?",
        },
    )

    assert prompt_response.status_code == status.HTTP_200_OK
    prompt_data = prompt_response.json()
    response_id = prompt_data["response"]["message_id"]

    # Step 3: Favorite the response
    favorite_response = test_client.post(
        f"/api/chat/responses/{response_id}/favorite",
        headers=auth_headers,
    )

    assert favorite_response.status_code == status.HTTP_200_OK
    favorite_data = favorite_response.json()
    assert favorite_data["favorited"] is True

    # Step 4: Check that the response is in the user's favorites list
    user_after_favorite = await mongodb.users.find_one({"_id": test_user.id})
    assert "favorite_response_ids" in user_after_favorite
    assert response_id in user_after_favorite["favorite_response_ids"]

    # Step 5: Get user's favorite responses
    favorites_response = test_client.get(
        "/api/users/me/favorites",
        headers=auth_headers,
    )

    assert favorites_response.status_code == status.HTTP_200_OK
    favorites_data = favorites_response.json()
    assert "favorites" in favorites_data
    assert len(favorites_data["favorites"]) >= 1

    # Find our favorited response
    found_favorite = False
    for favorite in favorites_data["favorites"]:
        if favorite["response"]["message_id"] == response_id:
            found_favorite = True
            break

    assert found_favorite, "Favorited response not found in favorites list"

    # Step 6: Unfavorite the response
    unfavorite_response = test_client.delete(
        f"/api/chat/responses/{response_id}/favorite",
        headers=auth_headers,
    )

    assert unfavorite_response.status_code == status.HTTP_200_OK
    unfavorite_data = unfavorite_response.json()
    assert unfavorite_data["favorited"] is False

    # Verify it's removed from the database
    user_after_unfavorite = await mongodb.users.find_one({"_id": test_user.id})
    assert response_id not in user_after_unfavorite["favorite_response_ids"]


@pytest.mark.asyncio
async def test_share_response_flow(test_client: TestClient, test_user, auth_headers, mongodb):
    """Test sharing responses: chat → share response → get share link → view shared content."""
    # Step 1: Create a chat session
    create_session_response = test_client.post(
        "/api/chat/sessions",
        headers=auth_headers,
        json={"mode": "solo", "humor_style": "absurd"},
    )

    assert create_session_response.status_code == status.HTTP_201_CREATED
    session_id = create_session_response.json()["id"]

    # Step 2: Submit a prompt to get a response
    prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": "What's the tallest mountain?",
        },
    )

    assert prompt_response.status_code == status.HTTP_200_OK
    prompt_data = prompt_response.json()
    response_id = prompt_data["response"]["message_id"]

    # Step 3: Share the response (FR-030, FR-031)
    share_response = test_client.post(
        f"/api/chat/responses/{response_id}/share",
        headers=auth_headers,
        json={
            "platform": "twitter",  # or "facebook", "whatsapp", etc.
        },
    )

    assert share_response.status_code == status.HTTP_200_OK
    share_data = share_response.json()
    assert "share_url" in share_data
    assert "share_id" in share_data

    share_id = share_data["share_id"]

    # Step 4: Get the shared content via share ID (simulating someone opening the share link)
    shared_content_response = test_client.get(
        f"/api/shares/{share_id}",
    )

    assert shared_content_response.status_code == status.HTTP_200_OK
    shared_content = shared_content_response.json()
    assert "prompt" in shared_content
    assert "response" in shared_content
    assert shared_content["prompt"]["text"] == "What's the tallest mountain?"
    assert "text" in shared_content["response"]
    assert "humor_style" in shared_content["response"]

    # Step 5: Verify the share was logged in the user's stats
    user_after_share = await mongodb.users.find_one({"_id": test_user.id})
    assert user_after_share["stats"]["total_shares"] >= 1

    # Step 6: Test share preview (for social media)
    preview_response = test_client.get(
        f"/api/shares/{share_id}/preview",
    )

    # This might return HTML or JSON depending on implementation
    assert preview_response.status_code == status.HTTP_200_OK


@pytest.mark.asyncio
async def test_sharing_to_multiple_platforms(test_client: TestClient, test_user, auth_headers, mongodb):
    """Test sharing the same response to multiple platforms."""
    # Create a session and get a response
    create_session_response = test_client.post(
        "/api/chat/sessions",
        headers=auth_headers,
        json={"mode": "solo", "humor_style": "absurd"},
    )

    session_id = create_session_response.json()["id"]

    prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": "What is the meaning of life?",
        },
    )

    response_id = prompt_response.json()["response"]["message_id"]

    # Share to multiple platforms
    platforms = ["twitter", "facebook", "linkedin"]
    share_ids = []

    for platform in platforms:
        share_response = test_client.post(
            f"/api/chat/responses/{response_id}/share",
            headers=auth_headers,
            json={"platform": platform},
        )

        assert share_response.status_code == status.HTTP_200_OK
        share_ids.append(share_response.json()["share_id"])

    # Verify all shares are valid
    for share_id in share_ids:
        shared_content_response = test_client.get(
            f"/api/shares/{share_id}",
        )
        assert shared_content_response.status_code == status.HTTP_200_OK

    # Verify the user's share count
    user_after_shares = await mongodb.users.find_one({"_id": test_user.id})
    assert user_after_shares["stats"]["total_shares"] >= len(platforms)


@pytest.mark.asyncio
async def test_favorite_and_share_permissions(test_client: TestClient, test_user, auth_headers, mongodb):
    """Test permissions for favoriting and sharing responses."""
    # Create a session and get a response
    create_session_response = test_client.post(
        "/api/chat/sessions",
        headers=auth_headers,
        json={"mode": "solo", "humor_style": "absurd"},
    )

    session_id = create_session_response.json()["id"]

    prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": "What is your favorite color?",
        },
    )

    response_id = prompt_response.json()["response"]["message_id"]

    # Create another user
    other_user = {
        "email": "<EMAIL>",
        "display_name": "Other User",
        "password_hash": "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # Hash for "testpassword123"
        "email_verified": True,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
    }

    await mongodb.users.insert_one(other_user)

    # Login as the other user
    other_login_response = test_client.post(
        "/api/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123",
        },
    )

    assert other_login_response.status_code == status.HTTP_200_OK
    other_token = other_login_response.json()["access_token"]
    other_headers = {"Authorization": f"Bearer {other_token}"}

    # Try to favorite the first user's response as the second user
    # This should still work - users can favorite any response they can see
    other_favorite_response = test_client.post(
        f"/api/chat/responses/{response_id}/favorite",
        headers=other_headers,
    )

    assert other_favorite_response.status_code == status.HTTP_200_OK

    # Try to favorite a non-existent response
    invalid_favorite_response = test_client.post(
        f"/api/chat/responses/nonexistent_id/favorite",
        headers=auth_headers,
    )

    assert invalid_favorite_response.status_code == status.HTTP_404_NOT_FOUND