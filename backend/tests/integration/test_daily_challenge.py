"""Integration test for daily challenge (Scenario 9 from quickstart.md).

This test validates FR-033, FR-034, FR-019 - complete challenge, streak increment, theme unlock.
"""

from datetime import datetime, timedelta

import pytest
from bson import ObjectId
from fastapi import status
from fastapi.testclient import TestClient


@pytest.mark.asyncio
async def test_daily_challenge_flow(test_client: TestClient, test_user, auth_headers, mongodb):
    """Test complete daily challenge flow: get challenge → complete → streak increment → theme unlock."""
    # Step 1: First, we need to create a daily challenge in the database
    today_id = datetime.utcnow().strftime("%Y%m%d")

    daily_challenge = {
        "challenge_id": today_id,
        "date": datetime.utcnow(),
        "theme_category": "history",
        "prompt": "What was the most important event in human history?",
        "difficulty": "medium",
        "completion_count": 0,
        "created_at": datetime.utcnow(),
    }

    await mongodb.daily_challenges.insert_one(daily_challenge)

    # Step 2: Get today's challenge
    today_challenge_response = test_client.get(
        "/api/challenges/today",
        headers=auth_headers,
    )

    assert today_challenge_response.status_code == status.HTTP_200_OK
    challenge_data = today_challenge_response.json()
    assert challenge_data["challenge_id"] == today_id
    assert challenge_data["prompt"] == "What was the most important event in human history?"
    assert "theme_category" in challenge_data
    assert challenge_data["theme_category"] == "history"

    # Step 3: Complete the challenge
    # First, create a chat session for the challenge
    create_session_response = test_client.post(
        "/api/chat/sessions",
        headers=auth_headers,
        json={
            "mode": "challenge",
            "humor_style": "absurd",
        },
    )

    assert create_session_response.status_code == status.HTTP_201_CREATED
    session_id = create_session_response.json()["id"]

    # Submit a prompt to the challenge session
    prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": challenge_data["prompt"],
        },
    )

    assert prompt_response.status_code == status.HTTP_200_OK
    prompt_data = prompt_response.json()
    response_id = prompt_data["response"]["message_id"]

    # Mark the challenge as completed
    complete_response = test_client.post(
        f"/api/challenges/{today_id}/complete",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "response_id": response_id,
        },
    )

    assert complete_response.status_code == status.HTTP_200_OK
    complete_data = complete_response.json()
    assert "streak_count" in complete_data
    assert complete_data["streak_count"] > 0  # Should be at least 1

    # Step 4: Check that the user's streak was incremented
    user_after_complete = await mongodb.users.find_one({"_id": test_user.id})
    assert user_after_complete["streak_count"] > 0
    assert "last_daily_challenge" in user_after_complete

    # Step 5: Test streak increment by completing multiple days
    # Create challenges for the past 6 days
    for i in range(1, 7):
        past_date = datetime.utcnow() - timedelta(days=i)
        past_id = past_date.strftime("%Y%m%d")

        # Create the challenge
        past_challenge = {
            "challenge_id": past_id,
            "date": past_date,
            "theme_category": "science",
            "prompt": f"Past challenge {i}",
            "difficulty": "easy",
            "completion_count": 0,
            "created_at": past_date,
        }

        await mongodb.daily_challenges.insert_one(past_challenge)

        # Simulate user completing this challenge in the past
        await mongodb.users.update_one(
            {"_id": test_user.id},
            {
                "$set": {
                    "last_daily_challenge": past_date,
                    "streak_count": i,
                }
            }
        )

    # Check user streak is now 6
    user_with_streak = await mongodb.users.find_one({"_id": test_user.id})
    assert user_with_streak["streak_count"] == 6

    # Complete today's challenge again (should simulate a 7-day streak)
    # First, reset the completion status
    await mongodb.daily_challenges.update_one(
        {"challenge_id": today_id},
        {"$set": {"user_completions": []}}
    )

    # Complete the challenge again
    complete_again_response = test_client.post(
        f"/api/challenges/{today_id}/complete",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "response_id": response_id,
        },
    )

    assert complete_again_response.status_code == status.HTTP_200_OK
    streak_data = complete_again_response.json()
    assert streak_data["streak_count"] == 7  # Now at 7 days

    # Step 6: Check theme unlocking after 7-day streak (FR-019)
    user_after_7_streak = await mongodb.users.find_one({"_id": test_user.id})

    # Check if the space-week theme is unlocked (from the seed data in database.py)
    assert "unlocked_themes" in user_after_7_streak
    assert "space-week" in user_after_7_streak["unlocked_themes"]

    # Verify theme unlocked message is in the response
    assert "unlocked_themes" in streak_data
    assert len(streak_data["unlocked_themes"]) > 0
    assert any("space" in theme.lower() for theme in streak_data["unlocked_themes"])


@pytest.mark.asyncio
async def test_challenge_completion_validation(test_client: TestClient, auth_headers, mongodb):
    """Test validation rules for challenge completion."""
    # Create a challenge for today
    today_id = datetime.utcnow().strftime("%Y%m%d")

    daily_challenge = {
        "challenge_id": today_id,
        "date": datetime.utcnow(),
        "theme_category": "history",
        "prompt": "Another test challenge",
        "difficulty": "easy",
        "completion_count": 0,
        "created_at": datetime.utcnow(),
    }

    await mongodb.daily_challenges.insert_one(daily_challenge)

    # Test completing with invalid challenge_id
    invalid_id_response = test_client.post(
        "/api/challenges/99999999/complete",
        headers=auth_headers,
        json={
            "session_id": str(ObjectId()),
            "response_id": "invalid_response_id",
        },
    )

    assert invalid_id_response.status_code == status.HTTP_404_NOT_FOUND

    # Test completing with invalid session_id
    invalid_session_response = test_client.post(
        f"/api/challenges/{today_id}/complete",
        headers=auth_headers,
        json={
            "session_id": str(ObjectId()),  # Valid format but doesn't exist
            "response_id": "test_response_id",
        },
    )

    assert invalid_session_response.status_code == status.HTTP_404_NOT_FOUND

    # Create a session to use for the next test
    create_session_response = test_client.post(
        "/api/chat/sessions",
        headers=auth_headers,
        json={
            "mode": "challenge",
            "humor_style": "absurd",
        },
    )

    assert create_session_response.status_code == status.HTTP_201_CREATED
    session_id = create_session_response.json()["id"]

    # Test completing with invalid response_id
    invalid_response_id_response = test_client.post(
        f"/api/challenges/{today_id}/complete",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "response_id": "nonexistent_response_id",
        },
    )

    assert invalid_response_id_response.status_code == status.HTTP_400_BAD_REQUEST


@pytest.mark.asyncio
async def test_multiple_theme_unlocks(test_client: TestClient, test_user, auth_headers, mongodb):
    """Test unlocking multiple themes with different criteria."""
    # Update user to have 10 prompts submitted (to test prompt_count unlock)
    await mongodb.users.update_one(
        {"_id": test_user.id},
        {"$set": {"stats.total_prompts": 10}}
    )

    # Create a new challenge
    challenge_id = "special_test_challenge"
    special_challenge = {
        "challenge_id": challenge_id,
        "date": datetime.utcnow() + timedelta(days=1),  # Tomorrow's challenge
        "theme_category": "special",
        "prompt": "Special challenge for theme unlock",
        "difficulty": "hard",
        "completion_count": 0,
        "created_at": datetime.utcnow(),
    }

    await mongodb.daily_challenges.insert_one(special_challenge)

    # Create a session for this challenge
    create_session_response = test_client.post(
        "/api/chat/sessions",
        headers=auth_headers,
        json={
            "mode": "challenge",
            "humor_style": "absurd",
        },
    )

    session_id = create_session_response.json()["id"]

    # Submit a prompt
    prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": special_challenge["prompt"],
        },
    )

    response_id = prompt_response.json()["response"]["message_id"]

    # Complete the challenge
    complete_response = test_client.post(
        f"/api/challenges/{challenge_id}/complete",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "response_id": response_id,
        },
    )

    assert complete_response.status_code == status.HTTP_200_OK

    # Verify both themes are unlocked (space-week from streak and back-to-school from prompt_count)
    user_after_unlock = await mongodb.users.find_one({"_id": test_user.id})
    assert "unlocked_themes" in user_after_unlock
    assert len(user_after_unlock["unlocked_themes"]) >= 2
    assert "back-to-school" in user_after_unlock["unlocked_themes"]  # From prompt_count: 10