"""Integration test for GDPR/LGPD data deletion (Scenario 16 from quickstart.md).

This test validates FR-041 - delete account → cascade to sessions/rooms → verify data removed.
"""

import time
from datetime import datetime

import pytest
from bson import ObjectId
from fastapi import status
from fastapi.testclient import TestClient


@pytest.mark.asyncio
async def test_account_deletion_flow(test_client: TestClient, mongodb):
    """Test GDPR/LGPD account deletion: create data → delete account → verify cascade deletion."""
    # Step 1: Create a test user specifically for deletion testing
    register_response = test_client.post(
        "/api/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "Password123!",
            "display_name": "Delete User",
            "consent_given": True,  # Required for GDPR/LGPD
        },
    )

    assert register_response.status_code == status.HTTP_201_CREATED
    user_data = register_response.json()
    user_id = user_data["user"]["id"]
    access_token = user_data["access_token"]
    auth_headers = {"Authorization": f"Bearer {access_token}"}

    # Step 2: Create data associated with the user

    # 2.1: Create chat sessions
    session_ids = []
    for i in range(3):
        create_session_response = test_client.post(
            "/api/chat/sessions",
            headers=auth_headers,
            json={"mode": "solo", "humor_style": "absurd"},
        )
        assert create_session_response.status_code == status.HTTP_201_CREATED
        session_ids.append(create_session_response.json()["id"])

    # 2.2: Add prompts to sessions
    for session_id in session_ids:
        prompt_response = test_client.post(
            "/api/chat/prompt",
            headers=auth_headers,
            json={
                "session_id": session_id,
                "prompt": "This is a test prompt that should be deleted",
            },
        )
        assert prompt_response.status_code == status.HTTP_200_OK

    # 2.3: Create a party room
    create_room_response = test_client.post(
        "/api/rooms",
        headers=auth_headers,
        json={"name": "Room to be deleted"},
    )
    assert create_room_response.status_code == status.HTTP_201_CREATED
    room_code = create_room_response.json()["room_code"]

    # 2.4: Favorite a response
    # Get the message ID from the first session
    session_response = test_client.get(
        f"/api/chat/sessions/{session_ids[0]}",
        headers=auth_headers,
    )
    session_data = session_response.json()
    response_message_id = None
    for msg in session_data["messages"]:
        if msg["type"] == "response":
            response_message_id = msg["message_id"]
            break

    assert response_message_id is not None

    # Favorite the response
    favorite_response = test_client.post(
        f"/api/chat/responses/{response_message_id}/favorite",
        headers=auth_headers,
    )
    assert favorite_response.status_code == status.HTTP_200_OK

    # Step 3: Verify all data exists before deletion
    # 3.1: Verify user exists
    user_before = await mongodb.users.find_one({"_id": ObjectId(user_id)})
    assert user_before is not None

    # 3.2: Verify chat sessions exist
    sessions_count = await mongodb.chat_sessions.count_documents({"user_id": ObjectId(user_id)})
    assert sessions_count == 3

    # 3.3: Verify room exists
    room_before = await mongodb.game_rooms.find_one({"room_code": room_code})
    assert room_before is not None
    assert room_before["creator_user_id"] == ObjectId(user_id)

    # Step 4: Request account deletion (with password confirmation)
    delete_response = test_client.delete(
        "/api/users/me",
        headers=auth_headers,
        json={"password": "Password123!"},
    )

    assert delete_response.status_code == status.HTTP_200_OK
    delete_data = delete_response.json()
    assert "success" in delete_data["message"].lower()

    # Step 5: Verify data has been deleted or anonymized
    # 5.1: Verify user is gone
    user_after = await mongodb.users.find_one({"_id": ObjectId(user_id)})
    assert user_after is None

    # 5.2: Verify chat sessions are gone
    sessions_after = await mongodb.chat_sessions.count_documents({"user_id": ObjectId(user_id)})
    assert sessions_after == 0

    # 5.3: Verify the rooms are either deleted or creator is anonymized
    room_after = await mongodb.game_rooms.find_one({"room_code": room_code})
    if room_after is not None:
        # The room might be kept but with anonymized creator
        assert room_after["creator_user_id"] != ObjectId(user_id)
    else:
        # Or the room might be deleted entirely
        pass

    # Step 6: Verify authentication no longer works
    profile_response = test_client.get(
        "/api/users/me",
        headers=auth_headers,
    )
    assert profile_response.status_code == status.HTTP_401_UNAUTHORIZED


@pytest.mark.asyncio
async def test_deletion_data_retention_compliance(test_client: TestClient, mongodb):
    """Test data retention compliance for deleted accounts."""
    # Create a user
    register_response = test_client.post(
        "/api/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "Password123!",
            "display_name": "Retention Test",
            "consent_given": True,
        },
    )

    user_data = register_response.json()
    user_id = user_data["user"]["id"]
    access_token = user_data["access_token"]
    auth_headers = {"Authorization": f"Bearer {access_token}"}

    # Create a chat session with the user
    create_session_response = test_client.post(
        "/api/chat/sessions",
        headers=auth_headers,
        json={"mode": "solo", "humor_style": "absurd"},
    )
    session_id = create_session_response.json()["id"]

    # Submit a prompt to that session
    prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": "This is a test prompt for data retention",
        },
    )
    assert prompt_response.status_code == status.HTTP_200_OK

    # Delete the account
    delete_response = test_client.delete(
        "/api/users/me",
        headers=auth_headers,
        json={"password": "Password123!"},
    )
    assert delete_response.status_code == status.HTTP_200_OK

    # Check that no personal data remains
    # 1. No user document
    user_after = await mongodb.users.find_one({"_id": ObjectId(user_id)})
    assert user_after is None

    # 2. No identifiable data in any collection
    email_query = {"$or": [
        {"email": "<EMAIL>"},
        {"user.email": "<EMAIL>"},
        {"participants.email": "<EMAIL>"},
    ]}

    email_matches = 0
    email_matches += await mongodb.users.count_documents(email_query)
    email_matches += await mongodb.chat_sessions.count_documents(email_query)
    email_matches += await mongodb.game_rooms.count_documents(email_query)

    assert email_matches == 0


@pytest.mark.asyncio
async def test_incomplete_deletion_handling(test_client: TestClient, mongodb):
    """Test handling of deletion when some operations fail."""
    # Create a user
    register_response = test_client.post(
        "/api/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "Password123!",
            "display_name": "Partial Delete",
            "consent_given": True,
        },
    )

    user_data = register_response.json()
    user_id = user_data["user"]["id"]
    access_token = user_data["access_token"]
    auth_headers = {"Authorization": f"Bearer {access_token}"}

    # Create some sessions
    for i in range(2):
        test_client.post(
            "/api/chat/sessions",
            headers=auth_headers,
            json={"mode": "solo", "humor_style": "absurd"},
        )

    # Delete with incorrect password (should fail)
    wrong_delete_response = test_client.delete(
        "/api/users/me",
        headers=auth_headers,
        json={"password": "WrongPassword123!"},
    )
    assert wrong_delete_response.status_code == status.HTTP_401_UNAUTHORIZED

    # Verify user and associated data still exist
    user_after_failed = await mongodb.users.find_one({"_id": ObjectId(user_id)})
    assert user_after_failed is not None

    sessions_after_failed = await mongodb.chat_sessions.count_documents({"user_id": ObjectId(user_id)})
    assert sessions_after_failed == 2

    # Now delete with correct password
    delete_response = test_client.delete(
        "/api/users/me",
        headers=auth_headers,
        json={"password": "Password123!"},
    )
    assert delete_response.status_code == status.HTTP_200_OK

    # Verify everything is gone
    user_gone = await mongodb.users.find_one({"_id": ObjectId(user_id)})
    assert user_gone is None

    sessions_gone = await mongodb.chat_sessions.count_documents({"user_id": ObjectId(user_id)})
    assert sessions_gone == 0