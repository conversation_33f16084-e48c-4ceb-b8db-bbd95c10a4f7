"""Integration test for empty prompt handling (Scenario 4 from quickstart.md).

This test validates friendly error message handling for empty prompts.
"""

import pytest
from fastapi import status
from fastapi.testclient import TestClient


@pytest.mark.asyncio
async def test_empty_prompt_handling(test_client: TestClient, test_user, auth_headers):
    """Test error handling when submitting an empty prompt."""
    # Step 1: Create a chat session
    create_session_response = test_client.post(
        "/api/chat/sessions",
        headers=auth_headers,
        json={"mode": "solo", "humor_style": "absurd"},
    )

    assert create_session_response.status_code == status.HTTP_201_CREATED
    session_data = create_session_response.json()
    session_id = session_data["id"]

    # Step 2: Submit an empty prompt
    empty_prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": "",  # Empty prompt
        },
    )

    # Verify the error response
    assert empty_prompt_response.status_code == status.HTTP_400_BAD_REQUEST
    error_data = empty_prompt_response.json()
    assert "detail" in error_data
    # Check that the error message is user-friendly
    assert "empty" in error_data["detail"].lower()
    assert "prompt" in error_data["detail"].lower()

    # Step 3: Submit a prompt with only whitespace
    whitespace_prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": "   ",  # Only whitespace
        },
    )

    # Verify the error response
    assert whitespace_prompt_response.status_code == status.HTTP_400_BAD_REQUEST
    error_data = whitespace_prompt_response.json()
    assert "detail" in error_data
    assert "empty" in error_data["detail"].lower() or "whitespace" in error_data["detail"].lower()

    # Step 4: Verify that valid prompts still work after error
    valid_prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": "This is a valid prompt",
        },
    )

    assert valid_prompt_response.status_code == status.HTTP_200_OK
    response_data = valid_prompt_response.json()
    assert "response" in response_data
    assert "text" in response_data["response"]


@pytest.mark.asyncio
async def test_missing_session_id_handling(test_client: TestClient, auth_headers):
    """Test error handling when session_id is missing."""
    # Submit a prompt without session_id
    no_session_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "prompt": "Valid prompt but no session",
        },
    )

    # Verify the error response
    assert no_session_response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    error_data = no_session_response.json()
    assert "detail" in error_data
    # Confirm the error message mentions session_id
    assert any("session_id" in error.get("loc", []) for error in error_data.get("detail", []))


@pytest.mark.asyncio
async def test_invalid_session_id_handling(test_client: TestClient, auth_headers):
    """Test error handling when session_id is invalid."""
    # Submit a prompt with an invalid session_id
    invalid_session_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": "invalid_session_id",  # Not a valid ObjectId
            "prompt": "Valid prompt but invalid session",
        },
    )

    # Verify the error response
    assert invalid_session_response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    error_data = invalid_session_response.json()
    assert "detail" in error_data
    # Confirm the error is related to invalid session_id format

    # Try with a valid format but non-existent session
    non_existent_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": "507f1f77bcf86cd799439011",  # Valid ObjectId format but doesn't exist
            "prompt": "Valid prompt but non-existent session",
        },
    )

    # Verify the error response
    assert non_existent_response.status_code == status.HTTP_404_NOT_FOUND
    error_data = non_existent_response.json()
    assert "detail" in error_data
    assert "not found" in error_data["detail"].lower() or "session" in error_data["detail"].lower()


@pytest.mark.asyncio
async def test_too_long_prompt_handling(test_client: TestClient, test_user, auth_headers):
    """Test error handling when prompt is too long."""
    # Create a chat session
    create_session_response = test_client.post(
        "/api/chat/sessions",
        headers=auth_headers,
        json={"mode": "solo", "humor_style": "absurd"},
    )

    assert create_session_response.status_code == status.HTTP_201_CREATED
    session_data = create_session_response.json()
    session_id = session_data["id"]

    # Create a very long prompt (10,000 characters)
    long_prompt = "x" * 10000

    # Submit the long prompt
    long_prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": long_prompt,
        },
    )

    # Check if there's a length limit enforced
    # Note: The actual limit might depend on the API implementation
    # Either it will succeed or return a specific error about length
    if long_prompt_response.status_code != status.HTTP_200_OK:
        assert long_prompt_response.status_code == status.HTTP_400_BAD_REQUEST
        error_data = long_prompt_response.json()
        assert "detail" in error_data
        assert "too long" in error_data["detail"].lower() or "length" in error_data["detail"].lower()