"""Integration test fixtures for the Hallucination Station API."""

import asyncio
from contextlib import asynccontextmanager
from datetime import datetime
from typing import AsyncGenerator, Any, Dict, List

import pytest
import pytest_asyncio
from bson import ObjectId
from fastapi import FastAPI
from fastapi.testclient import TestClient
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase

from backend.src.api import admin, auth, challenges, chat, leaderboard, prompts, rooms, users
from backend.src.api.error_handlers import (
    http_exception_handler,
    moderation_exception_handler,
    validation_exception_handler,
)
from backend.src.api.websocket import websocket_endpoint
from backend.src.core.database import Database
from backend.src.models.user import User, UserConsent
from backend.src.services.ai_service import AIService
from backend.src.services.auth_service import AuthService

# Mock AI service for testing
class MockAIService(AIService):
    """Mock AI service for testing."""

    def __init__(self):
        """Initialize mock AI service."""
        self.llm = "mock_llm"
        self.ready = True

    async def load_model(self) -> None:
        """Mock model loading."""
        self.ready = True

    async def generate_wrong_answer(
        self, prompt: str, humor_style: str = "random"
    ) -> Dict[str, Any]:
        """Mock wrong answer generation."""
        # Always return consistent response for testing
        return {
            "answer": f"Mock wrong answer for: {prompt} (style: {humor_style})",
            "confidence": 0.92,
            "humor_style": humor_style if humor_style != "random" else "absurd",
            "inference_time_ms": 150,
            "model": "mock-model",
        }

    async def refine_response(
        self, original_prompt: str, original_response: str, refinement: str
    ) -> Dict[str, Any]:
        """Mock response refinement."""
        return {
            "answer": f"Refined: {original_response} ({refinement})",
            "confidence": 0.95,
            "humor_style": "absurd",
            "inference_time_ms": 80,
            "model": "mock-model",
        }


# Test database config
TEST_MONGO_URL = "mongodb://localhost:27017"
TEST_DB_NAME = "hallucination_station_test"

# Global mock AI service
mock_ai_service = MockAIService()


@asynccontextmanager
async def mock_lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Mock lifespan context manager for testing."""
    # Connect to test database
    await Database.connect_db(TEST_MONGO_URL, TEST_DB_NAME)

    # Always clean the test database before each test
    await Database.db.command("dropDatabase")

    # Create indexes
    await Database.create_indexes()

    # Seed prompt library
    await Database.seed_prompt_library()

    # Set AI service
    app.state.ai_service = mock_ai_service

    yield

    # Cleanup
    await Database.close_db()


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the event loop for each test session."""
    policy = asyncio.get_event_loop_policy()
    loop = policy.new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="session")
async def test_app():
    """Create a FastAPI test application."""
    app = FastAPI(lifespan=mock_lifespan)

    # Register exception handlers
    app.add_exception_handler(ValueError, validation_exception_handler)
    app.add_exception_handler(Exception, http_exception_handler)

    # Register routers
    app.include_router(auth.router, prefix="/api/auth", tags=["auth"])
    app.include_router(users.router, prefix="/api/users", tags=["users"])
    app.include_router(chat.router, prefix="/api/chat", tags=["chat"])
    app.include_router(rooms.router, prefix="/api/rooms", tags=["rooms"])
    app.include_router(challenges.router, prefix="/api/challenges", tags=["challenges"])
    app.include_router(prompts.router, prefix="/api/prompts", tags=["prompts"])
    app.include_router(leaderboard.router, prefix="/api/leaderboard", tags=["leaderboard"])
    app.include_router(admin.router, prefix="/api/admin", tags=["admin"])

    # Add health check endpoint
    @app.get("/health")
    async def health_check():
        return {"status": "healthy", "database": "connected"}

    return app


@pytest_asyncio.fixture
async def test_client(test_app):
    """Create a test client for the FastAPI application."""
    async with test_app.router.lifespan_context(test_app):
        with TestClient(test_app) as client:
            yield client


@pytest_asyncio.fixture
async def mongodb():
    """Connect to test MongoDB and yield database."""
    client = AsyncIOMotorClient(TEST_MONGO_URL)
    db = client[TEST_DB_NAME]

    # Clear database
    await db.command("dropDatabase")

    yield db

    # Clean up
    await db.command("dropDatabase")
    client.close()


@pytest_asyncio.fixture
async def test_user(mongodb):
    """Create a test user for testing."""
    auth_service = AuthService(mongodb)

    # Create test user
    user = User(
        email="<EMAIL>",
        password_hash=auth_service.hash_password("testpassword123"),
        display_name="Test User",
        email_verified=True,
        verification_token=None,
        consent=UserConsent(
            given=True,
            date=datetime.utcnow(),
            ip_address="127.0.0.1",
        ),
    )

    result = await mongodb.users.insert_one(
        user.model_dump(by_alias=True, exclude={"id"})
    )
    user_id = result.inserted_id
    user.id = user_id

    return user


@pytest_asyncio.fixture
async def auth_tokens(test_client, test_user):
    """Get authentication tokens for the test user."""
    response = test_client.post(
        "/api/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123",
        },
    )
    tokens = response.json()
    return {
        "access_token": tokens["access_token"],
        "refresh_token": tokens["refresh_token"],
        "user_id": str(test_user.id),
    }


@pytest_asyncio.fixture
async def auth_headers(auth_tokens):
    """Get authentication headers with JWT token."""
    return {"Authorization": f"Bearer {auth_tokens['access_token']}"}