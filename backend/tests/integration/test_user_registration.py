"""Integration test for user registration flow (Scenario 1 from quickstart.md).

This test validates FR-001, FR-002, and FR-005 - email/password signup, verification, and login flow.
"""

import pytest
from fastapi import status
from fastapi.testclient import TestClient


@pytest.mark.asyncio
async def test_user_registration_flow(test_client: TestClient, mongodb):
    """Test complete user registration flow: register → verify → login."""
    # Step 1: Register a new user
    register_response = test_client.post(
        "/api/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "Password123!",
            "display_name": "New User",
            "consent_given": True,
        },
    )

    # Verify registration response
    assert register_response.status_code == status.HTTP_201_CREATED
    register_data = register_response.json()
    assert "access_token" in register_data
    assert "refresh_token" in register_data
    assert register_data["user"]["email"] == "<EMAIL>"
    assert register_data["user"]["display_name"] == "New User"
    assert register_data["user"]["email_verified"] is False

    # Step 2: Get verification token from database (in a real scenario, this would be sent by email)
    user_doc = await mongodb.users.find_one({"email": "<EMAIL>"})
    assert user_doc is not None
    assert "verification_token" in user_doc
    verification_token = user_doc["verification_token"]

    # Step 3: Verify the email with the token
    verify_response = test_client.post(
        f"/api/auth/verify-email/{verification_token}"
    )

    # Verify email verification response
    assert verify_response.status_code == status.HTTP_200_OK
    assert "Email verified" in verify_response.json()["message"]

    # Step 4: Check that user is now verified in the database
    user_doc = await mongodb.users.find_one({"email": "<EMAIL>"})
    assert user_doc["email_verified"] is True
    assert user_doc["verification_token"] is None

    # Step 5: Try to login with verified credentials
    login_response = test_client.post(
        "/api/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "Password123!",
        },
    )

    # Verify login response
    assert login_response.status_code == status.HTTP_200_OK
    login_data = login_response.json()
    assert "access_token" in login_data
    assert "refresh_token" in login_data
    assert login_data["user"]["email"] == "<EMAIL>"
    assert login_data["user"]["email_verified"] is True

    # Step 6: Test authorization with the token
    user_profile_response = test_client.get(
        "/api/users/me",
        headers={"Authorization": f"Bearer {login_data['access_token']}"},
    )

    # Verify profile access
    assert user_profile_response.status_code == status.HTTP_200_OK
    profile_data = user_profile_response.json()
    assert profile_data["email"] == "<EMAIL>"
    assert profile_data["display_name"] == "New User"


@pytest.mark.asyncio
async def test_registration_with_invalid_data(test_client: TestClient):
    """Test registration with invalid data."""
    # Test missing required fields
    missing_fields_response = test_client.post(
        "/api/auth/register",
        json={
            "email": "<EMAIL>",
            # Missing password, display_name, and consent
        },
    )
    assert missing_fields_response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY

    # Test without consent
    no_consent_response = test_client.post(
        "/api/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "Password123!",
            "display_name": "Invalid User",
            "consent_given": False,  # No consent
        },
    )
    assert no_consent_response.status_code == status.HTTP_400_BAD_REQUEST
    assert "consent" in no_consent_response.json()["detail"].lower()

    # Test invalid email format
    invalid_email_response = test_client.post(
        "/api/auth/register",
        json={
            "email": "not-an-email",
            "password": "Password123!",
            "display_name": "Invalid User",
            "consent_given": True,
        },
    )
    assert invalid_email_response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "email" in str(invalid_email_response.json()).lower()

    # Test invalid display name (special characters)
    invalid_name_response = test_client.post(
        "/api/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "Password123!",
            "display_name": "Invalid@User!",  # Contains special chars
            "consent_given": True,
        },
    )
    assert invalid_name_response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
    assert "display_name" in str(invalid_name_response.json()).lower()


@pytest.mark.asyncio
async def test_duplicate_email_registration(test_client: TestClient, test_user):
    """Test registration with an email that already exists."""
    # Try to register with the same email as test_user
    duplicate_response = test_client.post(
        "/api/auth/register",
        json={
            "email": "<EMAIL>",  # Same as test_user fixture
            "password": "NewPassword123!",
            "display_name": "Duplicate User",
            "consent_given": True,
        },
    )
    assert duplicate_response.status_code == status.HTTP_400_BAD_REQUEST
    assert "already registered" in duplicate_response.json()["detail"].lower()