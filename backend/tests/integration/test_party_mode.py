"""Integration test for party mode (Scenario 7+8 from quickstart.md).

This test validates FR-021 to FR-026 - create room, join, submit prompts, vote, top 3 results.
"""

import asyncio
from datetime import datetime

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from backend.src.models.user import User, UserConsent


@pytest.mark.asyncio
async def test_party_mode_flow(test_client: TestClient, mongodb, test_user, auth_headers):
    """Test complete party mode flow: create room → join (4 users) → submit prompts → vote → top 3 results."""
    # Step 1: Create additional test users for the party mode
    auth_service = test_client.post(
        "/api/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123",
        },
    ).json()

    user1_headers = {"Authorization": f"Bearer {auth_service['access_token']}"}

    # Create additional users
    additional_users = []
    additional_headers = []

    for i in range(3):  # Create 3 more users
        user = User(
            email=f"party_user{i}@example.com",
            display_name=f"Party User {i}",
            email_verified=True,
            consent=UserConsent(
                given=True,
                date=datetime.utcnow(),
                ip_address="127.0.0.1",
            ),
        )

        # Insert the user with password hash (same as in conftest.py)
        user_dict = user.model_dump(by_alias=True, exclude={"id"})
        user_dict["password_hash"] = "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW"  # Hash for "testpassword123"

        result = await mongodb.users.insert_one(user_dict)
        user_id = result.inserted_id
        user.id = user_id
        additional_users.append(user)

        # Login as this user to get auth token
        login_response = test_client.post(
            "/api/auth/login",
            json={
                "email": user.email,
                "password": "testpassword123",
            },
        )

        assert login_response.status_code == status.HTTP_200_OK
        token = login_response.json()["access_token"]
        additional_headers.append({"Authorization": f"Bearer {token}"})

    # Step 2: Create a game room (as the first user)
    create_room_response = test_client.post(
        "/api/rooms",
        headers=user1_headers,
        json={"name": "Test Party Room"},
    )

    assert create_room_response.status_code == status.HTTP_201_CREATED
    room_data = create_room_response.json()
    room_code = room_data["room_code"]
    assert len(room_code) == 6  # Verify 6-char room code
    assert room_data["creator_user_id"] == str(test_user.id)
    assert room_data["status"] == "waiting"
    assert len(room_data["participants"]) == 1  # Creator is automatically added

    # Step 3: Have additional users join the room
    for i, headers in enumerate(additional_headers):
        join_response = test_client.post(
            f"/api/rooms/{room_code}/join",
            headers=headers,
        )

        assert join_response.status_code == status.HTTP_200_OK
        room_status = join_response.json()
        # Each time someone joins, the participant count should increase
        assert len(room_status["participants"]) == 2 + i

        # Verify the user was added correctly
        participant_ids = [p["user_id"] for p in room_status["participants"]]
        assert str(additional_users[i].id) in participant_ids

    # Step 4: Check that the room is full with 4 participants
    room_response = test_client.get(
        f"/api/rooms/{room_code}",
        headers=user1_headers,
    )

    assert room_response.status_code == status.HTTP_200_OK
    room_data = room_response.json()
    assert len(room_data["participants"]) == 4  # Full room

    # Step 5: Test maximum participants (FR-043: max 4 participants)
    # Create a fifth user
    user5 = User(
        email="<EMAIL>",
        display_name="Fifth User",
        email_verified=True,
        consent=UserConsent(
            given=True,
            date=datetime.utcnow(),
            ip_address="127.0.0.1",
        ),
    )

    user5_dict = user5.model_dump(by_alias=True, exclude={"id"})
    user5_dict["password_hash"] = "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW"

    result = await mongodb.users.insert_one(user5_dict)
    user5.id = result.inserted_id

    login_response = test_client.post(
        "/api/auth/login",
        json={
            "email": user5.email,
            "password": "testpassword123",
        },
    )

    user5_token = login_response.json()["access_token"]
    user5_headers = {"Authorization": f"Bearer {user5_token}"}

    # Try to join full room
    join_response = test_client.post(
        f"/api/rooms/{room_code}/join",
        headers=user5_headers,
    )

    assert join_response.status_code == status.HTTP_400_BAD_REQUEST
    error = join_response.json()
    assert "full" in error["detail"].lower() or "maximum" in error["detail"].lower()

    # Step 6: Start submitting prompts
    # First, have the room creator start the round
    start_round_response = test_client.post(
        f"/api/rooms/{room_code}/start",
        headers=user1_headers,
    )

    assert start_round_response.status_code == status.HTTP_200_OK

    # Submit prompts from each user
    prompts = [
        "What is the tallest building in the world?",
        "Who invented the internet?",
        "What is the most popular sport?",
        "How do airplanes fly?",
    ]

    # First user submits
    submit_response1 = test_client.post(
        f"/api/rooms/{room_code}/prompt",
        headers=user1_headers,
        json={"prompt": prompts[0]},
    )
    assert submit_response1.status_code == status.HTTP_200_OK

    # Other users submit
    for i, headers in enumerate(additional_headers):
        submit_response = test_client.post(
            f"/api/rooms/{room_code}/prompt",
            headers=headers,
            json={"prompt": prompts[i+1]},
        )
        assert submit_response.status_code == status.HTTP_200_OK

    # Step 7: Get room status to see all responses
    responses_response = test_client.get(
        f"/api/rooms/{room_code}",
        headers=user1_headers,
    )

    assert responses_response.status_code == status.HTTP_200_OK
    room_with_responses = responses_response.json()

    # The current round should have responses
    current_round = room_with_responses["current_round"]
    assert current_round is not None
    assert "responses" in current_round
    assert len(current_round["responses"]) == 4  # One for each participant

    # Step 8: Cast votes
    # Each user votes for someone else's response
    # Extract response IDs for voting
    response_ids = [r["id"] for r in current_round["responses"]]

    # User 1 votes for response 2
    vote_response1 = test_client.post(
        f"/api/rooms/{room_code}/vote",
        headers=user1_headers,
        json={"response_id": response_ids[1]},
    )
    assert vote_response1.status_code == status.HTTP_200_OK

    # User 2 votes for response 3
    vote_response2 = test_client.post(
        f"/api/rooms/{room_code}/vote",
        headers=additional_headers[0],
        json={"response_id": response_ids[2]},
    )
    assert vote_response2.status_code == status.HTTP_200_OK

    # User 3 votes for response 4
    vote_response3 = test_client.post(
        f"/api/rooms/{room_code}/vote",
        headers=additional_headers[1],
        json={"response_id": response_ids[3]},
    )
    assert vote_response3.status_code == status.HTTP_200_OK

    # User 4 votes for response 1
    vote_response4 = test_client.post(
        f"/api/rooms/{room_code}/vote",
        headers=additional_headers[2],
        json={"response_id": response_ids[0]},
    )
    assert vote_response4.status_code == status.HTTP_200_OK

    # Step 9: End the round to get results
    end_round_response = test_client.post(
        f"/api/rooms/{room_code}/end-round",
        headers=user1_headers,
    )

    assert end_round_response.status_code == status.HTTP_200_OK
    round_results = end_round_response.json()

    # Verify that we have results with vote counts
    assert "top_responses" in round_results
    assert len(round_results["top_responses"]) > 0

    # Each response should have vote counts
    for response in round_results["top_responses"]:
        assert "vote_count" in response
        assert response["vote_count"] >= 0

    # The top 3 responses should be returned (FR-026)
    assert len(round_results["top_responses"]) <= 3


@pytest.mark.asyncio
async def test_joining_nonexistent_room(test_client: TestClient, auth_headers):
    """Test behavior when joining a non-existent room."""
    # Try to join a room that doesn't exist
    join_response = test_client.post(
        "/api/rooms/BADCDE/join",  # Non-existent room code
        headers=auth_headers,
    )

    assert join_response.status_code == status.HTTP_404_NOT_FOUND
    error = join_response.json()
    assert "not found" in error["detail"].lower() or "exist" in error["detail"].lower()


@pytest.mark.asyncio
async def test_party_room_expiry(test_client: TestClient, auth_headers, mongodb):
    """Test that party rooms expire after the TTL period."""
    # Create a room
    create_response = test_client.post(
        "/api/rooms",
        headers=auth_headers,
        json={"name": "Expiring Room"},
    )

    assert create_response.status_code == status.HTTP_201_CREATED
    room_code = create_response.json()["room_code"]

    # Verify the room has an expiry date
    room_doc = await mongodb.game_rooms.find_one({"room_code": room_code})
    assert room_doc is not None
    assert "expires_at" in room_doc

    # In a real test, we'd wait for the TTL to expire, but that's not practical
    # Instead, we'll manually remove the room to simulate expiry
    await mongodb.game_rooms.delete_one({"room_code": room_code})

    # Verify the room is gone
    get_response = test_client.get(
        f"/api/rooms/{room_code}",
        headers=auth_headers,
    )

    assert get_response.status_code == status.HTTP_404_NOT_FOUND


@pytest.mark.asyncio
async def test_prevent_voting_for_own_response(test_client: TestClient, mongodb, test_user, auth_headers):
    """Test that users cannot vote for their own responses."""
    # Create a room
    create_response = test_client.post(
        "/api/rooms",
        headers=auth_headers,
        json={"name": "Self Vote Test"},
    )

    assert create_response.status_code == status.HTTP_201_CREATED
    room_code = create_response.json()["room_code"]

    # Create another test user to join the room
    user2 = User(
        email="<EMAIL>",
        display_name="Self Vote Tester",
        email_verified=True,
        consent=UserConsent(
            given=True,
            date=datetime.utcnow(),
            ip_address="127.0.0.1",
        ),
    )

    user2_dict = user2.model_dump(by_alias=True, exclude={"id"})
    user2_dict["password_hash"] = "$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW"

    result = await mongodb.users.insert_one(user2_dict)
    user2.id = result.inserted_id

    login_response = test_client.post(
        "/api/auth/login",
        json={
            "email": user2.email,
            "password": "testpassword123",
        },
    )

    user2_token = login_response.json()["access_token"]
    user2_headers = {"Authorization": f"Bearer {user2_token}"}

    # Have user2 join the room
    join_response = test_client.post(
        f"/api/rooms/{room_code}/join",
        headers=user2_headers,
    )

    assert join_response.status_code == status.HTTP_200_OK

    # Start the round
    test_client.post(
        f"/api/rooms/{room_code}/start",
        headers=auth_headers,
    )

    # Both users submit prompts
    test_client.post(
        f"/api/rooms/{room_code}/prompt",
        headers=auth_headers,
        json={"prompt": "User 1's prompt"},
    )

    test_client.post(
        f"/api/rooms/{room_code}/prompt",
        headers=user2_headers,
        json={"prompt": "User 2's prompt"},
    )

    # Get the room to find response IDs
    room_response = test_client.get(
        f"/api/rooms/{room_code}",
        headers=auth_headers,
    )

    room_data = room_response.json()
    responses = room_data["current_round"]["responses"]

    # Find the response ID for user 1
    user1_response_id = None
    for response in responses:
        if response["user_id"] == str(test_user.id):
            user1_response_id = response["id"]
            break

    assert user1_response_id is not None

    # Try to have user 1 vote for their own response
    vote_response = test_client.post(
        f"/api/rooms/{room_code}/vote",
        headers=auth_headers,
        json={"response_id": user1_response_id},
    )

    # This should fail - users shouldn't be able to vote for their own responses
    assert vote_response.status_code == status.HTTP_400_BAD_REQUEST
    error = vote_response.json()
    assert "own response" in error["detail"].lower() or "self" in error["detail"].lower()