"""Integration test for solo chat mode (Scenario 3 from quickstart.md).

This test validates FR-008 to FR-012 - chat session, prompt submission, AI response <2s, humor style.
"""

import time
from datetime import datetime

import pytest
from fastapi import status
from fastapi.testclient import TestClient


@pytest.mark.asyncio
async def test_solo_chat_session(test_client: TestClient, test_user, auth_headers):
    """Test complete solo chat flow: create session → submit prompt → get response → change humor style."""
    # Step 1: Create a new chat session
    create_session_response = test_client.post(
        "/api/chat/sessions",
        headers=auth_headers,
        json={
            "mode": "solo",
            "humor_style": "absurd",
        },
    )

    assert create_session_response.status_code == status.HTTP_201_CREATED
    session_data = create_session_response.json()
    assert session_data["mode"] == "solo"
    assert session_data["humor_style"] == "absurd"
    assert session_data["is_active"] is True
    session_id = session_data["id"]

    # Step 2: Submit a prompt to the session
    prompt_start_time = datetime.utcnow()
    submit_prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": "What is the capital of France?",
        },
    )

    # Check response
    assert submit_prompt_response.status_code == status.HTTP_200_OK
    prompt_result = submit_prompt_response.json()

    # Verify response fields
    assert "prompt" in prompt_result
    assert prompt_result["prompt"]["text"] == "What is the capital of France?"
    assert "response" in prompt_result
    assert prompt_result["response"]["text"].startswith("Mock wrong answer")
    assert "humor_style" in prompt_result["response"]
    assert prompt_result["response"]["humor_style"] == "absurd"

    # Verify response time (FR-010: <2s latency)
    # Since we're using a mock, the time should be minimal
    prompt_end_time = datetime.utcnow()
    response_time_ms = (prompt_end_time - prompt_start_time).total_seconds() * 1000
    assert response_time_ms < 2000  # Less than 2s (2000ms)

    # Also check the inference_time_ms field from the response
    assert "inference_time_ms" in prompt_result["response"]
    assert prompt_result["response"]["inference_time_ms"] < 2000  # Less than 2s

    # Step 3: Change the humor style for the session (FR-011)
    update_style_response = test_client.patch(
        f"/api/chat/sessions/{session_id}",
        headers=auth_headers,
        json={
            "humor_style": "sarcastic",
        },
    )

    assert update_style_response.status_code == status.HTTP_200_OK
    updated_session = update_style_response.json()
    assert updated_session["humor_style"] == "sarcastic"

    # Step 4: Submit another prompt with the new humor style
    second_prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": "What is the tallest mountain?",
        },
    )

    assert second_prompt_response.status_code == status.HTTP_200_OK
    second_result = second_prompt_response.json()
    assert second_result["response"]["humor_style"] == "sarcastic"

    # Step 5: Get the chat history to verify both prompts are there
    history_response = test_client.get(
        f"/api/chat/sessions/{session_id}",
        headers=auth_headers,
    )

    assert history_response.status_code == status.HTTP_200_OK
    history = history_response.json()
    assert history["id"] == session_id
    assert len(history["messages"]) == 4  # 2 prompts + 2 responses

    # Step 6: Test refinement request (FR-015)
    refinement_response = test_client.post(
        "/api/chat/prompt/refine",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "response_id": second_result["response"]["message_id"],
            "refinement": "make it sillier",
        },
    )

    assert refinement_response.status_code == status.HTTP_200_OK
    refinement_result = refinement_response.json()
    assert "Refined:" in refinement_result["response"]["text"]
    assert "make it sillier" in refinement_result["response"]["text"]


@pytest.mark.asyncio
async def test_humor_style_options(test_client: TestClient, test_user, auth_headers):
    """Test different humor style options (FR-012)."""
    # Create sessions with different humor styles and verify they work
    humor_styles = ["absurd", "sarcastic", "whimsical", "childlike", "random"]

    for style in humor_styles:
        # Create session with this humor style
        create_response = test_client.post(
            "/api/chat/sessions",
            headers=auth_headers,
            json={
                "mode": "solo",
                "humor_style": style,
            },
        )

        assert create_response.status_code == status.HTTP_201_CREATED
        session_data = create_response.json()
        assert session_data["humor_style"] == style
        session_id = session_data["id"]

        # Submit a prompt to test the style
        prompt_response = test_client.post(
            "/api/chat/prompt",
            headers=auth_headers,
            json={
                "session_id": session_id,
                "prompt": f"Test prompt with {style} style",
            },
        )

        assert prompt_response.status_code == status.HTTP_200_OK
        result = prompt_response.json()

        # For "random" style, the actual style could be any of the others
        if style == "random":
            assert result["response"]["humor_style"] in humor_styles[:-1]  # Any except "random"
        else:
            assert result["response"]["humor_style"] == style


@pytest.mark.asyncio
async def test_chat_session_listing(test_client: TestClient, test_user, auth_headers):
    """Test listing chat sessions for a user (FR-013, FR-014)."""
    # Create multiple chat sessions
    for i in range(3):
        test_client.post(
            "/api/chat/sessions",
            headers=auth_headers,
            json={
                "mode": "solo",
                "humor_style": "absurd",
            },
        )

    # Get session list
    sessions_response = test_client.get(
        "/api/chat/sessions",
        headers=auth_headers,
    )

    assert sessions_response.status_code == status.HTTP_200_OK
    sessions_data = sessions_response.json()
    assert "sessions" in sessions_data
    assert "total" in sessions_data
    assert sessions_data["total"] >= 3  # At least the 3 we just created
    assert len(sessions_data["sessions"]) >= 3

    # Test pagination
    paginated_response = test_client.get(
        "/api/chat/sessions?limit=2&offset=0",
        headers=auth_headers,
    )

    assert paginated_response.status_code == status.HTTP_200_OK
    paginated_data = paginated_response.json()
    assert len(paginated_data["sessions"]) == 2  # Limited to 2