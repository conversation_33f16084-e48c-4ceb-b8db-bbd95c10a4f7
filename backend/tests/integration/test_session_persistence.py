"""Integration test for session persistence (Scenario 13 from quickstart.md).

This test validates FR-006 and FR-045 - login → chat → refresh page → session restored → network disconnect → reconnect.
"""

import asyncio
import time
from datetime import datetime, timedelta

import pytest
from fastapi import status
from fastapi.testclient import Test<PERSON>lient
from jose import jwt

from backend.src.services.auth_service import SECRET_KEY, ALGORITHM


@pytest.mark.asyncio
async def test_token_refresh_flow(test_client: TestClient, test_user, auth_headers, mongodb):
    """Test token refresh flow (FR-006)."""
    # Step 1: Login to get initial tokens
    login_response = test_client.post(
        "/api/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123",
        },
    )

    assert login_response.status_code == status.HTTP_200_OK
    tokens = login_response.json()
    access_token = tokens["access_token"]
    refresh_token = tokens["refresh_token"]

    # Step 2: Use access token to access a protected endpoint
    profile_response = test_client.get(
        "/api/users/me",
        headers={"Authorization": f"Bearer {access_token}"},
    )

    assert profile_response.status_code == status.HTTP_200_OK
    assert profile_response.json()["email"] == "<EMAIL>"

    # Step 3: Simulate access token expiry
    # Decode the token
    payload = jwt.decode(access_token, SECRET_KEY, algorithms=[ALGORITHM])
    # Modify the expiry time to be in the past
    payload["exp"] = datetime.utcnow() - timedelta(minutes=5)
    # Create a new expired token
    expired_token = jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)

    # Step 4: Try using the expired token
    expired_response = test_client.get(
        "/api/users/me",
        headers={"Authorization": f"Bearer {expired_token}"},
    )

    assert expired_response.status_code == status.HTTP_401_UNAUTHORIZED

    # Step 5: Refresh token to get new access token
    refresh_response = test_client.post(
        "/api/auth/refresh",
        json={"refresh_token": refresh_token},
    )

    assert refresh_response.status_code == status.HTTP_200_OK
    new_tokens = refresh_response.json()
    assert "access_token" in new_tokens
    assert "refresh_token" in new_tokens
    new_access_token = new_tokens["access_token"]

    # Step 6: Use new access token
    new_profile_response = test_client.get(
        "/api/users/me",
        headers={"Authorization": f"Bearer {new_access_token}"},
    )

    assert new_profile_response.status_code == status.HTTP_200_OK
    assert new_profile_response.json()["email"] == "<EMAIL>"


@pytest.mark.asyncio
async def test_session_persistence(test_client: TestClient, test_user, auth_headers, mongodb):
    """Test session persistence across page refreshes and reconnections (FR-045)."""
    # Step 1: Create a chat session
    create_session_response = test_client.post(
        "/api/chat/sessions",
        headers=auth_headers,
        json={"mode": "solo", "humor_style": "absurd"},
    )

    assert create_session_response.status_code == status.HTTP_201_CREATED
    session_id = create_session_response.json()["id"]

    # Step 2: Submit a prompt to the session
    prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": "What is the capital of France?",
        },
    )

    assert prompt_response.status_code == status.HTTP_200_OK
    first_response = prompt_response.json()

    # Step 3: Simulate page refresh by getting the session again
    session_response = test_client.get(
        f"/api/chat/sessions/{session_id}",
        headers=auth_headers,
    )

    assert session_response.status_code == status.HTTP_200_OK
    session_data = session_response.json()
    assert session_data["id"] == session_id
    assert len(session_data["messages"]) == 2  # 1 prompt + 1 response

    # Verify the message content is preserved
    message_types = [msg["type"] for msg in session_data["messages"]]
    assert "prompt" in message_types
    assert "response" in message_types

    for msg in session_data["messages"]:
        if msg["type"] == "prompt":
            assert msg["prompt"]["text"] == "What is the capital of France?"
        elif msg["type"] == "response":
            assert "text" in msg["response"]
            assert "humor_style" in msg["response"]

    # Step 4: Add another message to the session
    second_prompt_response = test_client.post(
        "/api/chat/prompt",
        headers=auth_headers,
        json={
            "session_id": session_id,
            "prompt": "What is the tallest mountain?",
        },
    )

    assert second_prompt_response.status_code == status.HTTP_200_OK

    # Step 5: Simulate network disconnect and reconnect by getting the session after a delay
    # In a real test, this would involve closing and reopening the WebSocket connection
    time.sleep(1)  # Short delay to simulate disconnect/reconnect

    reconnect_response = test_client.get(
        f"/api/chat/sessions/{session_id}",
        headers=auth_headers,
    )

    assert reconnect_response.status_code == status.HTTP_200_OK
    reconnect_data = reconnect_response.json()
    assert reconnect_data["id"] == session_id
    assert len(reconnect_data["messages"]) == 4  # 2 prompts + 2 responses

    # Verify all message content is preserved
    prompt_texts = []
    for msg in reconnect_data["messages"]:
        if msg["type"] == "prompt":
            prompt_texts.append(msg["prompt"]["text"])

    assert "What is the capital of France?" in prompt_texts
    assert "What is the tallest mountain?" in prompt_texts


@pytest.mark.asyncio
async def test_session_listing_persistence(test_client: TestClient, test_user, auth_headers, mongodb):
    """Test that sessions are persisted and can be listed after login."""
    # Step 1: Create multiple chat sessions
    session_ids = []
    for i in range(3):
        create_response = test_client.post(
            "/api/chat/sessions",
            headers=auth_headers,
            json={"mode": "solo", "humor_style": "absurd"},
        )

        assert create_response.status_code == status.HTTP_201_CREATED
        session_ids.append(create_response.json()["id"])

    # Step 2: Add prompts to each session
    for i, session_id in enumerate(session_ids):
        prompt_response = test_client.post(
            "/api/chat/prompt",
            headers=auth_headers,
            json={
                "session_id": session_id,
                "prompt": f"Test prompt {i}",
            },
        )
        assert prompt_response.status_code == status.HTTP_200_OK

    # Step 3: Logout (simulate by getting a new token)
    new_login_response = test_client.post(
        "/api/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123",
        },
    )

    assert new_login_response.status_code == status.HTTP_200_OK
    new_token = new_login_response.json()["access_token"]
    new_headers = {"Authorization": f"Bearer {new_token}"}

    # Step 4: Get sessions list after "logging in" with new token
    sessions_response = test_client.get(
        "/api/chat/sessions",
        headers=new_headers,
    )

    assert sessions_response.status_code == status.HTTP_200_OK
    sessions_data = sessions_response.json()
    assert "sessions" in sessions_data
    assert sessions_data["total"] >= 3

    # Verify all our created sessions are in the list
    found_sessions = 0
    for session in sessions_data["sessions"]:
        if session["id"] in session_ids:
            found_sessions += 1

    assert found_sessions >= 3

    # Step 5: Get a specific session by ID
    specific_session_response = test_client.get(
        f"/api/chat/sessions/{session_ids[0]}",
        headers=new_headers,
    )

    assert specific_session_response.status_code == status.HTTP_200_OK
    specific_session = specific_session_response.json()
    assert specific_session["id"] == session_ids[0]
    assert len(specific_session["messages"]) == 2  # 1 prompt + 1 response