"""
WebSocket load testing for party mode.
Tests 25 concurrent game rooms (4 users each) and validates real-time message delivery.
"""
import asyncio
import random
import string
import time
import json
import argparse
import statistics
from typing import List, Dict, Any, Tuple
import socketio
import aiohttp
import logging
import sys
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("websocket_load_test.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Sample prompts to use during testing
SAMPLE_PROMPTS = [
    "What is the capital of France?",
    "Who invented the light bulb?",
    "How do airplanes fly?",
    "Tell me a joke about programming",
    "What's the meaning of life?",
    "How does photosynthesis work?",
    "Why is the sky blue?",
    "What's the tallest mountain on Earth?",
    "How do computers work?",
    "Explain quantum physics simply",
]

# Constants for the test
NUM_ROOMS = 25
USERS_PER_ROOM = 4
TOTAL_USERS = NUM_ROOMS * USERS_PER_ROOM
BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

class RoomParticipant:
    """Represents a user participating in a game room"""

    def __init__(self, user_id: str, display_name: str, room_code: str, token: str):
        self.user_id = user_id
        self.display_name = display_name
        self.room_code = room_code
        self.token = token
        self.sio = socketio.AsyncClient()
        self.connected = False
        self.messages_received = []
        self.prompt_sent = False
        self.vote_sent = False
        self.connection_time = None
        self.event_times = {}

        # Register event handlers
        self.sio.on("user_joined")(self.on_user_joined)
        self.sio.on("response_generated")(self.on_response_generated)
        self.sio.on("vote_cast")(self.on_vote_cast)
        self.sio.on("round_complete")(self.on_round_complete)

    async def connect(self) -> bool:
        """Connect to WebSocket with authentication token"""
        try:
            start_time = time.time()
            await self.sio.connect(
                f"{WS_URL}?token={self.token}&room_code={self.room_code}",
                headers={"Authorization": f"Bearer {self.token}"}
            )
            self.connection_time = time.time() - start_time
            self.connected = True
            logger.info(f"User {self.display_name} connected to room {self.room_code}")
            return True
        except Exception as e:
            logger.error(f"Connection failed for {self.display_name}: {e}")
            return False

    async def disconnect(self):
        """Disconnect from WebSocket"""
        if self.connected:
            await self.sio.disconnect()
            self.connected = False

    async def send_prompt(self, prompt: str):
        """Send a prompt to the room"""
        if not self.connected:
            return False

        try:
            event_time = time.time()
            await self.sio.emit("submit_prompt", {
                "room_code": self.room_code,
                "prompt": prompt
            })
            self.prompt_sent = True
            self.event_times["prompt_sent"] = event_time
            logger.info(f"User {self.display_name} sent prompt to room {self.room_code}")
            return True
        except Exception as e:
            logger.error(f"Error sending prompt for {self.display_name}: {e}")
            return False

    async def vote(self, response_id: str):
        """Cast a vote for a response"""
        if not self.connected:
            return False

        try:
            event_time = time.time()
            await self.sio.emit("cast_vote", {
                "room_code": self.room_code,
                "response_id": response_id
            })
            self.vote_sent = True
            self.event_times["vote_sent"] = event_time
            logger.info(f"User {self.display_name} cast vote in room {self.room_code}")
            return True
        except Exception as e:
            logger.error(f"Error voting for {self.display_name}: {e}")
            return False

    # WebSocket event handlers
    async def on_user_joined(self, data):
        """Handle user_joined event"""
        self.messages_received.append(("user_joined", time.time(), data))
        self.event_times["user_joined_received"] = time.time()
        logger.debug(f"User {self.display_name} received user_joined event in room {self.room_code}")

    async def on_response_generated(self, data):
        """Handle response_generated event"""
        self.messages_received.append(("response_generated", time.time(), data))
        self.event_times["response_generated"] = time.time()
        logger.debug(f"User {self.display_name} received response_generated event in room {self.room_code}")

    async def on_vote_cast(self, data):
        """Handle vote_cast event"""
        self.messages_received.append(("vote_cast", time.time(), data))
        self.event_times["vote_received"] = time.time()
        logger.debug(f"User {self.display_name} received vote_cast event in room {self.room_code}")

    async def on_round_complete(self, data):
        """Handle round_complete event"""
        self.messages_received.append(("round_complete", time.time(), data))
        self.event_times["round_complete"] = time.time()
        logger.debug(f"User {self.display_name} received round_complete event in room {self.room_code}")

async def register_and_login(session: aiohttp.ClientSession, user_num: int) -> Tuple[str, str]:
    """Register and login a user, return token and user_id"""
    # Register
    username = f"ws_loadtest_user_{user_num}"
    email = f"{username}@loadtest.com"
    password = "LoadTest123!"

    register_data = {
        "email": email,
        "password": password,
        "display_name": username,
        "consent_to_terms": True
    }

    async with session.post(f"{BASE_URL}/api/auth/register", json=register_data) as response:
        if response.status != 200:
            logger.error(f"Registration failed for {username}: {await response.text()}")
            return None, None

    # Login
    login_data = {
        "email": email,
        "password": password
    }

    async with session.post(f"{BASE_URL}/api/auth/login", json=login_data) as response:
        if response.status != 200:
            logger.error(f"Login failed for {username}: {await response.text()}")
            return None, None

        data = await response.json()
        token = data.get("access_token")
        user_id = data.get("user_id")

        return token, user_id

async def create_room(session: aiohttp.ClientSession, token: str) -> str:
    """Create a game room and return the room code"""
    headers = {"Authorization": f"Bearer {token}"}

    async with session.post(f"{BASE_URL}/api/rooms", headers=headers) as response:
        if response.status != 200:
            logger.error(f"Room creation failed: {await response.text()}")
            return None

        data = await response.json()
        return data.get("room_code")

async def join_room(session: aiohttp.ClientSession, token: str, room_code: str) -> bool:
    """Join an existing game room"""
    headers = {"Authorization": f"Bearer {token}"}

    async with session.post(f"{BASE_URL}/api/rooms/{room_code}/join", headers=headers) as response:
        if response.status != 200:
            logger.error(f"Room join failed: {await response.text()}")
            return False

        return True

async def run_room_test(room_index: int) -> Dict[str, Any]:
    """Run test for a single room with multiple participants"""
    room_metrics = {
        "room_index": room_index,
        "participants": [],
        "connection_times": [],
        "message_delivery_times": {},
        "total_messages": 0,
        "errors": []
    }

    # Setup HTTP session
    async with aiohttp.ClientSession() as session:
        # Create participants
        participants = []
        room_code = None

        # First participant creates the room
        token, user_id = await register_and_login(session, room_index * USERS_PER_ROOM)
        if not token:
            room_metrics["errors"].append(f"Failed to register first user for room {room_index}")
            return room_metrics

        display_name = f"user_{room_index * USERS_PER_ROOM}"
        room_code = await create_room(session, token)

        if not room_code:
            room_metrics["errors"].append(f"Failed to create room {room_index}")
            return room_metrics

        first_participant = RoomParticipant(user_id, display_name, room_code, token)
        participants.append(first_participant)

        # Register other participants and join room
        for i in range(1, USERS_PER_ROOM):
            user_num = room_index * USERS_PER_ROOM + i
            token, user_id = await register_and_login(session, user_num)

            if not token:
                room_metrics["errors"].append(f"Failed to register user {user_num} for room {room_index}")
                continue

            display_name = f"user_{user_num}"

            # Join the room
            join_success = await join_room(session, token, room_code)
            if not join_success:
                room_metrics["errors"].append(f"Failed to join room for user {user_num}")
                continue

            participant = RoomParticipant(user_id, display_name, room_code, token)
            participants.append(participant)

        # Connect all participants to WebSocket
        connect_tasks = [p.connect() for p in participants]
        connect_results = await asyncio.gather(*connect_tasks)

        # Record connection times
        for p in participants:
            if p.connection_time:
                room_metrics["connection_times"].append(p.connection_time)

        # Let everyone receive the user_joined events
        await asyncio.sleep(2)

        # Have each participant send a prompt
        for i, p in enumerate(participants):
            if p.connected:
                prompt = SAMPLE_PROMPTS[i % len(SAMPLE_PROMPTS)]
                await p.send_prompt(prompt)

        # Wait for responses to be generated
        await asyncio.sleep(5)

        # Have participants vote
        vote_tasks = []
        for p in participants:
            if p.connected and len(p.messages_received) > 0:
                # Find a response to vote for
                response_events = [msg for msg in p.messages_received if msg[0] == "response_generated"]
                if response_events:
                    # Get a random response that isn't their own
                    response_data = response_events[0][2]
                    responses = response_data.get("responses", [])
                    if responses:
                        # Filter out own response if possible
                        other_responses = [r for r in responses if r.get("user_id") != p.user_id]
                        if other_responses:
                            response_to_vote = random.choice(other_responses)
                        else:
                            response_to_vote = random.choice(responses)

                        vote_tasks.append(p.vote(response_to_vote.get("id")))

        if vote_tasks:
            await asyncio.gather(*vote_tasks)

        # Wait for round to complete
        await asyncio.sleep(3)

        # Calculate metrics for this room
        for p in participants:
            participant_metrics = {
                "user_id": p.user_id,
                "display_name": p.display_name,
                "connected": p.connected,
                "messages_received": len(p.messages_received),
                "event_times": p.event_times
            }
            room_metrics["participants"].append(participant_metrics)
            room_metrics["total_messages"] += len(p.messages_received)

        # Calculate message delivery times
        for p in participants:
            if "prompt_sent" in p.event_times and "response_generated" in p.event_times:
                delay = p.event_times["response_generated"] - p.event_times["prompt_sent"]
                if "prompt_to_response" not in room_metrics["message_delivery_times"]:
                    room_metrics["message_delivery_times"]["prompt_to_response"] = []
                room_metrics["message_delivery_times"]["prompt_to_response"].append(delay)

            if "vote_sent" in p.event_times and "vote_received" in p.event_times:
                delay = p.event_times["vote_received"] - p.event_times["vote_sent"]
                if "vote_cast_to_received" not in room_metrics["message_delivery_times"]:
                    room_metrics["message_delivery_times"]["vote_cast_to_received"] = []
                room_metrics["message_delivery_times"]["vote_cast_to_received"].append(delay)

        # Disconnect all participants
        disconnect_tasks = [p.disconnect() for p in participants]
        await asyncio.gather(*disconnect_tasks)

        return room_metrics

async def run_load_test() -> Dict[str, Any]:
    """
    Run the complete WebSocket load test with multiple rooms
    """
    logger.info(f"Starting WebSocket load test with {NUM_ROOMS} rooms, {USERS_PER_ROOM} users per room")
    start_time = time.time()

    # Run tests for all rooms in parallel
    room_tasks = [run_room_test(i) for i in range(NUM_ROOMS)]
    room_results = await asyncio.gather(*room_tasks)

    end_time = time.time()
    total_duration = end_time - start_time

    # Combine all metrics
    connection_times = []
    message_delivery_times = {
        "prompt_to_response": [],
        "vote_cast_to_received": []
    }
    total_messages = 0
    total_errors = []
    total_participants = 0
    connected_participants = 0

    for room in room_results:
        connection_times.extend(room["connection_times"])
        total_messages += room["total_messages"]
        total_errors.extend(room["errors"])

        for key, times in room["message_delivery_times"].items():
            if key in message_delivery_times:
                message_delivery_times[key].extend(times)

        for p in room["participants"]:
            total_participants += 1
            if p["connected"]:
                connected_participants += 1

    # Calculate result statistics
    results = {
        "timestamp": datetime.now().isoformat(),
        "configuration": {
            "num_rooms": NUM_ROOMS,
            "users_per_room": USERS_PER_ROOM,
            "total_users": TOTAL_USERS,
            "base_url": BASE_URL,
            "ws_url": WS_URL
        },
        "results": {
            "total_duration_seconds": total_duration,
            "total_messages": total_messages,
            "total_participants": total_participants,
            "connected_participants": connected_participants,
            "connection_rate": connected_participants / total_participants if total_participants else 0,
            "total_errors": len(total_errors),
            "errors": total_errors
        },
        "connection_times": {
            "samples": len(connection_times),
            "min_seconds": min(connection_times) if connection_times else None,
            "max_seconds": max(connection_times) if connection_times else None,
            "avg_seconds": statistics.mean(connection_times) if connection_times else None,
            "median_seconds": statistics.median(connection_times) if connection_times else None,
            "p95_seconds": sorted(connection_times)[int(len(connection_times) * 0.95)] if connection_times else None
        }
    }

    # Add message delivery statistics
    results["message_delivery"] = {}
    for key, times in message_delivery_times.items():
        if times:
            results["message_delivery"][key] = {
                "samples": len(times),
                "min_seconds": min(times),
                "max_seconds": max(times),
                "avg_seconds": statistics.mean(times),
                "median_seconds": statistics.median(times),
                "p95_seconds": sorted(times)[int(len(times) * 0.95)]
            }

    # Determine if test passed
    connection_rate_threshold = 0.95  # 95% connection success rate
    message_delivery_time_threshold = 1.0  # 1 second

    passed = (
        results["results"]["connection_rate"] >= connection_rate_threshold and
        len(total_errors) <= NUM_ROOMS * 0.1 and  # Allow 10% of rooms to have errors
        (
            "prompt_to_response" not in results["message_delivery"] or
            results["message_delivery"]["prompt_to_response"]["p95_seconds"] <= message_delivery_time_threshold
        )
    )

    results["passed"] = passed
    return results

async def main(output_file: str = None):
    """
    Main function to run the WebSocket load test.

    Args:
        output_file: Optional file to write detailed results
    """
    try:
        # Run the load test
        results = await run_load_test()

        # Print summary
        logger.info("\n===== WebSocket Load Test Results =====")
        logger.info(f"Test {'PASSED' if results['passed'] else 'FAILED'}")
        logger.info(f"Total duration: {results['results']['total_duration_seconds']:.2f} seconds")
        logger.info(f"Connection success rate: {results['results']['connection_rate'] * 100:.2f}%")
        logger.info(f"Total messages exchanged: {results['results']['total_messages']}")
        logger.info(f"Total errors: {results['results']['total_errors']}")

        if "prompt_to_response" in results["message_delivery"]:
            prompt_stats = results["message_delivery"]["prompt_to_response"]
            logger.info(f"Prompt to response p95 time: {prompt_stats['p95_seconds']:.2f} seconds")

        if "vote_cast_to_received" in results["message_delivery"]:
            vote_stats = results["message_delivery"]["vote_cast_to_received"]
            logger.info(f"Vote broadcast p95 time: {vote_stats['p95_seconds']:.2f} seconds")

        # Write detailed results to file if requested
        if output_file:
            import json
            with open(output_file, 'w') as f:
                json.dump(results, f, indent=2)
            logger.info(f"Detailed results written to {output_file}")

    except Exception as e:
        logger.error(f"Test failed with error: {e}", exc_info=True)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="WebSocket Load Testing")
    parser.add_argument("--output", "-o", help="Output file for detailed results")
    parser.add_argument("--rooms", "-r", type=int, help=f"Number of rooms (default: {NUM_ROOMS})")
    parser.add_argument("--users", "-u", type=int, help=f"Users per room (default: {USERS_PER_ROOM})")
    parser.add_argument("--url", help=f"Base API URL (default: {BASE_URL})")
    parser.add_argument("--ws", help=f"WebSocket URL (default: {WS_URL})")
    args = parser.parse_args()

    if args.rooms:
        NUM_ROOMS = args.rooms

    if args.users:
        USERS_PER_ROOM = args.users

    if args.url:
        BASE_URL = args.url

    if args.ws:
        WS_URL = args.ws

    # Update total users
    TOTAL_USERS = NUM_ROOMS * USERS_PER_ROOM

    # Run the test
    asyncio.run(main(args.output))