"""
AI Service performance test.
Measures response time for 20 different prompts and validates <2s p95 AI response time (FR-010).
"""
import asyncio
import time
import statistics
import argparse
import sys
import os
from typing import List, Dict, Any

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../..")))

# Will be imported when running from project root
from backend.src.services.ai_service import AIService
from backend.src.core.config import settings

# Sample prompts to test with
TEST_PROMPTS = [
    "What's the tallest mountain in the world?",
    "Who invented the internet?",
    "How do planes fly?",
    "What is the capital of Brazil?",
    "Why is the sky blue?",
    "What's the boiling point of water?",
    "Who wrote Romeo and Juliet?",
    "How do solar panels work?",
    "What is the largest planet in our solar system?",
    "How many bones are in the human body?",
    "What are black holes?",
    "How do computers store data?",
    "What is photosynthesis?",
    "Who painted the Mona Lisa?",
    "What's the speed of light?",
    "How do vaccines work?",
    "What's the deepest part of the ocean?",
    "How do earthquakes happen?",
    "What is the longest river in the world?",
    "How does GPS work?"
]

# Available humor styles
HUMOR_STYLES = ["silly", "dry", "sarcastic", "absurd", "dark"]

async def test_ai_latency(ai_service: AIService) -> Dict[str, Any]:
    """
    Test AI latency by sending multiple prompts and measuring response time.

    Args:
        ai_service: Instance of the AIService

    Returns:
        Dictionary with test results including response times and statistics
    """
    latencies = []
    responses = []

    print(f"Running AI latency test with {len(TEST_PROMPTS)} prompts...")

    for i, prompt in enumerate(TEST_PROMPTS):
        humor_style = HUMOR_STYLES[i % len(HUMOR_STYLES)]

        # Measure response time
        start_time = time.time()
        response = await ai_service.generate_wrong_answer(prompt, humor_style)
        end_time = time.time()

        latency = (end_time - start_time) * 1000  # Convert to ms
        latencies.append(latency)
        responses.append(response)

        print(f"Prompt {i+1}/{len(TEST_PROMPTS)}: {latency:.2f}ms - '{prompt[:30]}...'")

    # Calculate statistics
    avg_latency = statistics.mean(latencies)
    median_latency = statistics.median(latencies)
    p95_latency = sorted(latencies)[int(len(latencies) * 0.95)]
    max_latency = max(latencies)
    min_latency = min(latencies)

    results = {
        "total_prompts": len(TEST_PROMPTS),
        "avg_latency_ms": avg_latency,
        "median_latency_ms": median_latency,
        "p95_latency_ms": p95_latency,
        "max_latency_ms": max_latency,
        "min_latency_ms": min_latency,
        "passing": p95_latency < 2000,  # FR-010: <2s p95 AI response time
        "responses": responses,
        "latencies": latencies
    }

    return results

async def main(output_file: str = None):
    """
    Main function to run the AI latency test.

    Args:
        output_file: Optional file to write detailed results
    """
    # Initialize AI service
    ai_service = AIService()
    await ai_service.init()

    try:
        # Run the test
        results = await test_ai_latency(ai_service)

        # Print summary
        print("\n===== AI Latency Test Results =====")
        print(f"Total prompts: {results['total_prompts']}")
        print(f"Average latency: {results['avg_latency_ms']:.2f}ms")
        print(f"Median latency: {results['median_latency_ms']:.2f}ms")
        print(f"95th percentile latency: {results['p95_latency_ms']:.2f}ms")
        print(f"Maximum latency: {results['max_latency_ms']:.2f}ms")
        print(f"Minimum latency: {results['min_latency_ms']:.2f}ms")

        # Check FR-010 requirement (<2s p95 latency)
        if results['passing']:
            print("\n✅ PASSED: 95th percentile latency is less than 2 seconds (FR-010)")
        else:
            print("\n❌ FAILED: 95th percentile latency exceeds 2 seconds (FR-010)")

        # Write detailed results to file if requested
        if output_file:
            import json
            with open(output_file, 'w') as f:
                # Remove large response data from file output
                file_results = {k: v for k, v in results.items() if k != 'responses'}
                json.dump(file_results, f, indent=2)
            print(f"Detailed results written to {output_file}")

    finally:
        # Clean up
        await ai_service.shutdown()

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="AI Service Latency Testing")
    parser.add_argument("--output", "-o", help="Output file for detailed results")
    args = parser.parse_args()

    asyncio.run(main(args.output))