"""
Load testing for chat API endpoints using Locust.
Tests 100 concurrent users and validates <200ms p95 API latency (FR-042).
"""
from locust import HttpUser, task, between
import json
import random

# Sample prompts to use during load testing
SAMPLE_PROMPTS = [
    "What is the capital of France?",
    "Who invented the light bulb?",
    "How do airplanes fly?",
    "Tell me a joke about programming",
    "What's the meaning of life?",
    "How does photosynthesis work?",
    "Why is the sky blue?",
    "What's the tallest mountain on Earth?",
    "How do computers work?",
    "Explain quantum physics simply",
]

# Available humor styles for variety in testing
HUMOR_STYLES = ["silly", "dry", "sarcastic", "absurd", "dark"]

class ChatUser(HttpUser):
    """
    Simulates users interacting with the chat API.
    Each user creates a session, sends prompts, and checks their history.
    """
    wait_time = between(1, 5)  # Wait between 1-5 seconds between tasks

    def on_start(self):
        """Setup: Register and login before starting tasks"""
        # Register a random user
        username = f"loadtest_user_{random.randint(10000, 99999)}"
        email = f"{username}@loadtest.com"
        password = "LoadTest123!"

        register_data = {
            "email": email,
            "password": password,
            "display_name": username,
            "consent_to_terms": True
        }

        self.client.post("/api/auth/register", json=register_data)

        # Login to get token
        login_data = {
            "email": email,
            "password": password
        }

        login_response = self.client.post("/api/auth/login", json=login_data)
        response_data = json.loads(login_response.text)
        self.token = response_data.get("access_token")

        # Create a chat session
        self.headers = {"Authorization": f"Bearer {self.token}"}
        session_data = {"humor_style": random.choice(HUMOR_STYLES)}

        session_response = self.client.post(
            "/api/chat/sessions",
            json=session_data,
            headers=self.headers
        )

        session_data = json.loads(session_response.text)
        self.session_id = session_data.get("id")

    @task(3)  # Higher weight for the main chat functionality
    def send_prompt(self):
        """Send a random prompt to the AI and get a response"""
        if not hasattr(self, 'session_id'):
            return  # Skip if setup failed

        prompt_data = {
            "session_id": self.session_id,
            "prompt": random.choice(SAMPLE_PROMPTS),
            "humor_style": random.choice(HUMOR_STYLES)
        }

        self.client.post(
            "/api/chat/prompt",
            json=prompt_data,
            headers=self.headers,
            name="/api/chat/prompt"  # For grouping in statistics
        )

    @task(1)
    def get_chat_history(self):
        """Get chat history for the current session"""
        if not hasattr(self, 'session_id'):
            return  # Skip if setup failed

        self.client.get(
            f"/api/chat/sessions/{self.session_id}",
            headers=self.headers,
            name="/api/chat/sessions/:id"  # For grouping in statistics
        )

    @task(1)
    def get_all_sessions(self):
        """Get all chat sessions for the user"""
        if not hasattr(self, 'token'):
            return  # Skip if setup failed

        self.client.get(
            "/api/chat/sessions",
            headers=self.headers,
            name="/api/chat/sessions"  # For grouping in statistics
        )

if __name__ == "__main__":
    """
    Run with: locust -f backend/tests/load/test_chat_load.py --host=http://localhost:8000

    Check results in Locust web interface at http://localhost:8089

    Validation criteria:
    - p95 response time < 200ms for all endpoints (FR-042)
    - No errors during 100 concurrent user test
    """
    pass