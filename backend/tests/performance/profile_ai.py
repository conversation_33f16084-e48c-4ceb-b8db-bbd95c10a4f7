"""
Profiling script for AI service.
Uses py-spy for CPU profiling and memory_profiler for memory usage tracking.
Validates <4GB RAM usage during model loading and inference.

Requirements:
- py-spy: pip install py-spy
- memory_profiler: pip install memory_profiler
- matplotlib: pip install matplotlib

Usage:
    # Run memory profiling
    python -m memory_profiler backend/tests/performance/profile_ai.py --memory

    # Run CPU profiling (requires sudo or proper permissions for py-spy)
    python backend/tests/performance/profile_ai.py --cpu
"""
import os
import sys
import time
import argparse
import asyncio
import subprocess
import random
from typing import List, Dict, Any

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../..")))

# Import memory_profiler if available (for memory profiling)
try:
    from memory_profiler import profile as memory_profile
    memory_profiler_available = True
except ImportError:
    memory_profiler_available = False
    print("Warning: memory_profiler not installed. Install with: pip install memory_profiler")

# Sample prompts for testing
TEST_PROMPTS = [
    "What's the tallest mountain in the world?",
    "Who invented the internet?",
    "How do planes fly?",
    "What is the capital of Brazil?",
    "Why is the sky blue?",
    "What's the boiling point of water?",
    "Who wrote Romeo and Juliet?",
    "How do solar panels work?",
    "What is the largest planet in our solar system?",
    "How many bones are in the human body?"
]

# Available humor styles
HUMOR_STYLES = ["silly", "dry", "sarcastic", "absurd", "dark"]

def cpu_profile():
    """
    Run CPU profiling using py-spy
    """
    print("Starting CPU profiling with py-spy...")

    # Define the command to run the AI service test
    cmd = [sys.executable, "-c", """
import asyncio
import os
import sys
import time
import random

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../..")))

from backend.src.services.ai_service import AIService

# Sample prompts and humor styles
TEST_PROMPTS = [
    "What's the tallest mountain in the world?",
    "Who invented the internet?",
    "How do planes fly?",
    "What is the capital of Brazil?",
    "Why is the sky blue?",
    "What's the boiling point of water?",
    "Who wrote Romeo and Juliet?",
    "How do solar panels work?",
    "What is the largest planet in our solar system?",
    "How many bones are in the human body?"
]

HUMOR_STYLES = ["silly", "dry", "sarcastic", "absurd", "dark"]

async def run_ai_service_test():
    # Initialize AI service
    print("Initializing AI service...")
    ai_service = AIService()
    await ai_service.init()

    try:
        print("Running inference tests...")
        # Send multiple prompts to exercise the AI service
        for i, prompt in enumerate(TEST_PROMPTS):
            humor_style = HUMOR_STYLES[i % len(HUMOR_STYLES)]
            print(f"Processing prompt {i+1}/{len(TEST_PROMPTS)}: {prompt[:30]}...")

            # Generate response
            response = await ai_service.generate_wrong_answer(prompt, humor_style)

            # Simulate processing time to allow profiler to capture data
            time.sleep(0.5)

    finally:
        # Clean up
        await ai_service.shutdown()

# Run the test
asyncio.run(run_ai_service_test())
"""]

    # Output files
    svg_output = os.path.join(os.path.dirname(__file__), "ai_service_profile.svg")
    txt_output = os.path.join(os.path.dirname(__file__), "ai_service_profile.txt")

    # Run py-spy
    try:
        # First try the SVG output (visualization)
        print(f"Generating SVG flame graph to {svg_output}...")
        subprocess.run(
            ["py-spy", "record", "--output", svg_output, "--format", "flamegraph", "--rate", "100", "--"] + cmd,
            check=True
        )

        # Then generate text output (for easier analysis)
        print(f"Generating text profile to {txt_output}...")
        with open(txt_output, "w") as f:
            subprocess.run(
                ["py-spy", "top", "--duration", "20", "--rate", "100", "--"] + cmd,
                check=True, stdout=f
            )

        print(f"CPU profiling complete. Results saved to {svg_output} and {txt_output}")
        print("Open the SVG file in a web browser to view the flame graph.")

    except FileNotFoundError:
        print("Error: py-spy not found. Install with: pip install py-spy")
        print("Note: py-spy may require sudo/admin permissions to profile processes.")
    except subprocess.CalledProcessError as e:
        print(f"Error running py-spy: {e}")
        print("Note: py-spy may require sudo/admin permissions to profile processes.")

# Define the memory profiling function
if memory_profiler_available:
    @memory_profile
    def memory_profile_ai_service():
        """
        Profile memory usage of AI service initialization and inference
        """
        print("Starting memory profiling...")

        # Import here to avoid early loading
        from backend.src.services.ai_service import AIService

        # Run the async code with asyncio
        asyncio.run(memory_profile_ai_service_async())

    async def memory_profile_ai_service_async():
        """
        Async part of the memory profiling
        """
        # Initialize AI service
        print("Initializing AI service...")
        ai_service = AIService()

        print("Loading AI model...")
        await ai_service.init()

        # Record peak memory after initialization
        print("Model loaded. Running inference tests...")

        try:
            # Process sample prompts
            for i, prompt in enumerate(TEST_PROMPTS):
                humor_style = HUMOR_STYLES[i % len(HUMOR_STYLES)]
                print(f"Processing prompt {i+1}/{len(TEST_PROMPTS)}: {prompt[:30]}...")

                # Generate response (memory-intensive operation)
                response = await ai_service.generate_wrong_answer(prompt, humor_style)

                # Log sample response
                print(f"Sample response (first 100 chars): {str(response)[:100]}...")

                # Force garbage collection to get accurate memory readings
                import gc
                gc.collect()

        finally:
            # Clean up
            print("Shutting down AI service...")
            await ai_service.shutdown()
else:
    def memory_profile_ai_service():
        print("Error: memory_profiler not available. Install with: pip install memory_profiler")

def main():
    parser = argparse.ArgumentParser(description="AI Service Profiling")
    parser.add_argument("--cpu", action="store_true", help="Run CPU profiling with py-spy")
    parser.add_argument("--memory", action="store_true", help="Run memory profiling with memory_profiler")
    args = parser.parse_args()

    # Default to memory profiling if no args provided
    if not (args.cpu or args.memory):
        print("No profiling method specified. Use --cpu or --memory.")
        parser.print_help()
        return

    if args.cpu:
        cpu_profile()

    if args.memory and memory_profiler_available:
        memory_profile_ai_service()
    elif args.memory:
        print("Error: memory_profiler not available. Install with: pip install memory_profiler")

if __name__ == "__main__":
    main()